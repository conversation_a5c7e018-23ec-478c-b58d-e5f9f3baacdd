#!/usr/bin/env python3
"""
调试FCL问题的具体脚本
"""

import os
import sys

# 确保能够导入utils模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_specific_job():
    """调试特定job的数据"""
    print("=" * 60)
    print("调试 pro2_system_id=86532, job_id=71973")
    print("=" * 60)
    
    try:
        # 导入必要模块
        from utils.database.db_pro2_sea_air_profit import query_job_details_by_date
        from utils.basic.logger_config import setup_logger
        
        # 设置详细日志
        logger = setup_logger(
            name=__name__,
            level="debug", 
            log_to_console=True,
            log_to_file=False
        )
        
        print("尝试查询job基础数据...")
        
        # 使用更大的日期范围
        results = query_job_details_by_date(
            begin_date="2024-01-01",
            end_date="2024-12-31",
            logger_prefix="[DEBUG]"
        )
        
        print(f"查询到 {len(results)} 条记录")
        
        # 查找目标job
        target_jobs = []
        for job in results:
            job_id = job.get('job_id') or job.get('job_file_id')
            if job_id == 71973:
                target_jobs.append(job)
        
        if target_jobs:
            print(f"\n找到 {len(target_jobs)} 条 job_id=71973 的记录")
            for i, job in enumerate(target_jobs):
                print(f"\n记录 {i+1}:")
                for key, value in job.items():
                    print(f"  {key}: {value}")
        else:
            print(f"\n未找到 job_id=71973 的记录")
            print("显示前3条记录供参考:")
            for i, job in enumerate(results[:3]):
                job_id = job.get('job_id') or job.get('job_file_id')
                print(f"  Record {i+1}: job_id={job_id}")
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_specific_job()