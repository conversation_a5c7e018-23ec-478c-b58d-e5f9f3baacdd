#!/usr/bin/env python3
"""
调试青岛系统(86532)的指定货逻辑问题
"""

import os
import sys
import pandas as pd

# 确保能够导入utils模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_qd_nominated_logic():
    """模拟青岛系统的指定货过滤逻辑"""
    print("=" * 60)
    print("调试青岛系统(86532)指定货逻辑")
    print("=" * 60)
    
    # 模拟测试数据 - 基于你提供的期望结果构造
    # 期望: bk_count=3 teu=4 bill_count=3 nomi_count=2
    # 实际: bk_count=1 teu=0 nomi_count=0
    test_data = [
        # Case 1: FCL, is_free_hand=0 (应该被统计为指定货)
        {'job_file_id': 71973, 'is_free_hand': 0, 'salesman_dept_name': '业务一部', 'business_type': 'sea_export', 'lcl_rt': 0, 'teu': 2, 'profit': 1000},
        # Case 2: FCL, is_free_hand=0 (应该被统计为指定货)
        {'job_file_id': 71973, 'is_free_hand': 0, 'salesman_dept_name': '业务二部', 'business_type': 'sea_export', 'lcl_rt': 0, 'teu': 2, 'profit': 500},
        # Case 3: FCL, is_free_hand=1 (不应该被统计为指定货)
        {'job_file_id': 71973, 'is_free_hand': 1, 'salesman_dept_name': '业务三部', 'business_type': 'sea_export', 'lcl_rt': 0, 'teu': 0, 'profit': 300},
    ]
    
    job_bookings = pd.DataFrame(test_data)
    print(f"测试数据: {len(job_bookings)} 条booking")
    print(job_bookings[['is_free_hand', 'salesman_dept_name', 'teu', 'profit']])
    
    # 模拟青岛系统的环境
    print(f"\n模拟环境: LOCATION=QD (青岛)")
    current_location = 'QD'
    location_to_system_id = {
        'QD': 86532,  # 青岛
        'SH': 86021,  # 上海
        'HK': 852,    # 香港
        'TY': 8103    # 东京
    }
    current_pro2_system_id = location_to_system_id.get(current_location, 86021)
    print(f"当前系统ID: {current_pro2_system_id}")
    
    # 应用指定货过滤逻辑
    if 'is_free_hand' in job_bookings.columns:
        if current_pro2_system_id == 86021:
            # 86021特例逻辑
            print("应用86021特例逻辑...")
            # [这里是86021的特例处理代码]
            pass
        else:
            # 其他系统使用原始逻辑
            print("应用标准逻辑 (非86021系统)...")
            all_nominated_bookings = job_bookings[job_bookings['is_free_hand'].fillna(1) == 0]
            print(f"指定货过滤条件: is_free_hand == 0")
    else:
        all_nominated_bookings = pd.DataFrame()
    
    print(f"\n过滤后的指定货数据: {len(all_nominated_bookings)} 条")
    if len(all_nominated_bookings) > 0:
        print(all_nominated_bookings[['is_free_hand', 'salesman_dept_name', 'teu', 'profit']])
    
    # 计算统计数据
    print(f"\n计算统计数据:")
    
    # 基础计数 (应该基于全部booking数据)
    bk_count = len(job_bookings)
    bl_count = len(job_bookings)  # 简化假设
    total_teu = job_bookings['teu'].fillna(0).sum()
    
    print(f"  bk_count (全部booking): {bk_count}")
    print(f"  bl_count (全部BL): {bl_count}")  
    print(f"  total_teu (全部TEU): {total_teu}")
    
    # 指定货统计
    nomi_count = len(all_nominated_bookings)
    nomi_teu = all_nominated_bookings['teu'].fillna(0).sum() if len(all_nominated_bookings) > 0 else 0
    nomi_profit = all_nominated_bookings['profit'].fillna(0).sum() if len(all_nominated_bookings) > 0 else 0
    
    print(f"  nomi_count (指定货数量): {nomi_count}")
    print(f"  nomi_teu (指定货TEU): {nomi_teu}")
    print(f"  nomi_profit (指定货利润): {nomi_profit}")
    
    print(f"\n期望vs实际对比:")
    print(f"  bk_count: 期望=3, 测试数据={bk_count}, 实际结果=1")
    print(f"  teu: 期望=4, 测试数据={total_teu}, 实际结果=0")  
    print(f"  nomi_count: 期望=2, 测试数据={nomi_count}, 实际结果=0")
    
    print(f"\n问题分析:")
    if bk_count != 1:
        print(f"  ✓ bk_count不匹配说明实际查询到的booking数量不是3条，而是1条")
    if total_teu == 0:
        print(f"  ✓ 实际结果teu=0可能是因为:")
        print(f"    - 查询到的booking中teu字段都为0")  
        print(f"    - 或者teu字段名不匹配")
    if nomi_count == 0:
        print(f"  ✓ 实际结果nomi_count=0说明:")
        print(f"    - 查询到的booking中没有is_free_hand=0的记录")
        print(f"    - 或者is_free_hand字段名不匹配/值不正确")
        
    print(f"\n结论:")
    print(f"真正的问题很可能是:")
    print(f"1. 数据查询层面：实际只查询到1条booking，而不是期望的3条")
    print(f"2. 字段匹配问题：TEU字段可能名称不匹配或值为0")
    print(f"3. 指定货字段问题：is_free_hand字段可能名称不匹配或值不正确")

if __name__ == "__main__":
    debug_qd_nominated_logic()