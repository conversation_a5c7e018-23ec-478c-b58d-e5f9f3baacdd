# 统计QD PRO2系统的海运&空运损益-根据时间周期
"""
工作档类型的定义：
1: 海运出口
2: 海运进口
3: 特别工作档
4: 海运三角
5: 空运出口
6: 空运进口

业务类型(service_mode)的定义：
海运：
1: LCL (CFS/CFS)
2: FCL (CY/CY)
3: BUY-CONSOL (CFS/CY)
0: 其他

空运：
4: AIR

出口货物类型(cargo_type)的定义:
service_mode = 1/3时
1: 散货集拼
2: 散货同行
3-4: 其他

service_mode = 2时
1. 柜货船公司
2. 柜货同行
"""

import os
import logging
import pandas as pd
from datetime import datetime, timedelta
from dotenv import load_dotenv
from utils.basic.fb_conn import get_pooled_pro2_connection, DatabaseError, execute_pro2_query
from typing import Dict, List, Tuple, Any
from utils.basic.logger_config import setup_logger

load_dotenv(override=True)

# 设置日志
logger = setup_logger(
    name=__name__,
    level="warning",
    log_to_console=True,
    log_to_file=True
)

# 性能优化配置
ENABLE_QUERY_OPTIMIZATION = os.getenv('ENABLE_QUERY_OPTIMIZATION', 'true').lower() == 'true'
ENABLE_FIELD_OPTIMIZATION = os.getenv('ENABLE_FIELD_OPTIMIZATION', 'true').lower() == 'true'

# SQL查询语句定义

# 工作档利润查询SQL - 统一通用查询，基于job_date过滤
SQL_JOB_PROFIT_UNIFIED = '''
    SELECT
        icn.job_file_id,
        jf.job_file_no,
        -- local_amount大于0的为收入，小于0的为成本
        SUM(CASE WHEN icn.local_amount > 0 THEN icn.local_amount ELSE 0 END) as income,
        SUM(CASE WHEN icn.local_amount < 0 THEN icn.local_amount ELSE 0 END) as cost,
        SUM(icn.local_amount) as job_profit
    FROM invoice_cost_note icn
    JOIN job_file jf ON icn.job_file_id = jf.id
    WHERE icn.is_valid = 1
    AND jf.is_active = 1
    AND icn.job_date BETWEEN ? AND ?
    GROUP BY icn.job_file_id, jf.job_file_no
'''

# 原有的booking级别利润查询SQL
SQL_SEA_EXPORT_PROFIT = '''
    WITH filtered_inv_cn AS (
        SELECT
            invoice_cost_note.id as inv_cn_id
        FROM
            invoice_cost_note
            JOIN sea_export_job_file
                ON invoice_cost_note.job_file_id = sea_export_job_file.JOB_file_id
        WHERE
            job_date BETWEEN ? AND ?
            AND is_valid = 1
            AND status <> 3
    ),
    bk_rt AS (
        SELECT
            seb.id as bk_id,
            seb.JOB_FILE_ID,
            COALESCE((SELECT SUM(CASE
                                        WHEN cs.name LIKE '%20%' THEN 1*1000
                                        WHEN cs.name LIKE '%40%' THEN 2*1000
                                        WHEN cs.name LIKE '%45%' THEN 2*1000
                                        ELSE 0
                                     END)
                          FROM sea_export_bk_container sebc
                          LEFT JOIN container_size cs ON sebc.size_id = cs.id
                          WHERE sebc.bk_id = seb.id), 0) as rt
        FROM
            sea_export_booking seb
        WHERE
            seb.service_mode = 2
            AND seb.is_valid = 1
        UNION ALL
		SELECT
		    seb.id as bk_id,
		    seb.job_file_id,
		    COALESCE(maxvalue(coalesce(t1.cbm,0), coalesce(t1.kgs,0)/1000, 1), 0) AS rt
		FROM
		    sea_export_booking seb
		    LEFT JOIN sea_export_bk_cfs_tonnage t1 ON t1.bk_id = seb.id 
		        AND (t1.type_id = (
		            SELECT MAX(t2.type_id)
		            FROM sea_export_bk_cfs_tonnage t2
		            WHERE t2.bk_id = seb.id
		        ) OR NOT EXISTS (
		            SELECT 1 
		            FROM sea_export_bk_cfs_tonnage t3 
		            WHERE t3.bk_id = seb.id
		        ))
		WHERE
		    (seb.service_mode = 1 OR seb.service_mode = 3)
		    AND seb.is_valid = 1
    ),
    filtered_charges AS (
        SELECT
            inv_cn_id,
            line_no,
            local_amount
        FROM
            inv_cn_charge
            JOIN filtered_inv_cn USING (inv_cn_id)
    ),
    all_bookings AS (
        SELECT
            fc.inv_cn_id,
            fc.line_no,
            fc.local_amount,
            bl.bk_id
        FROM
            filtered_charges fc
            JOIN inv_cn_charge_sea_export_bl icb ON fc.inv_cn_id = icb.inv_cn_id
                AND fc.line_no = icb.line_no
            JOIN sea_export_bk_bl bl ON icb.bl_id = bl.bl_id
        UNION ALL
        SELECT
            fc.inv_cn_id,
            fc.line_no,
            fc.local_amount,
            cb.bk_id
        FROM
            filtered_charges fc
            JOIN inv_cn_charge_sea_export_ctr icc ON fc.inv_cn_id = icc.inv_cn_id
                AND fc.line_no = icc.line_no
            JOIN sea_export_container_bk cb ON icc.container_id = cb.container_id
        UNION ALL
        SELECT
            fc.inv_cn_id,
            fc.line_no,
            fc.local_amount,
            ib.bk_id
        FROM
            filtered_charges fc
            JOIN inv_cn_sea_export_bk ib ON fc.inv_cn_id = ib.inv_cn_id
            LEFT JOIN inv_cn_charge_sea_export_bl icb ON fc.inv_cn_id = icb.inv_cn_id
                AND fc.line_no = icb.line_no
            LEFT JOIN inv_cn_charge_sea_export_ctr icc ON fc.inv_cn_id = icc.inv_cn_id
                AND fc.line_no = icc.line_no
        WHERE
            icb.inv_cn_id IS NULL
            AND icc.inv_cn_id IS NULL
    )
    SELECT
        ab.inv_cn_id,
        ab.line_no,
        ab.local_amount,
        CAST(ab.bk_id AS VARCHAR(50)) as jb_id,
        MAXVALUE(COALESCE(br.rt,0), 1) AS rt
    FROM
        all_bookings ab
        JOIN bk_rt br ON ab.bk_id = br.bk_id
    ORDER BY
        ab.inv_cn_id,
        ab.line_no
'''

SQL_SEA_IMPORT_PROFIT = '''
    WITH filtered_inv_cn AS (
        SELECT
            invoice_cost_note.id as inv_cn_id
        FROM
            invoice_cost_note
            JOIN sea_import_job_file
                ON invoice_cost_note.job_file_id = sea_import_job_file.JOB_file_id
        WHERE
            job_date BETWEEN ? AND ?
            AND is_valid = 1
            AND status <> 3
    ),
    bl_rt AS (
		SELECT
		    bc.job_file_id,
		    bc.bl_id,
		    COALESCE(SUM(CASE
		                    WHEN sibc.is_cfs = 0 AND cs.name LIKE '%20%' THEN 1*1000
		                    WHEN sibc.is_cfs = 0 AND cs.name LIKE '%40%' THEN 2*1000
		                    WHEN sibc.is_cfs = 0 AND cs.name LIKE '%45%' THEN 2*1000
		                    ELSE 0
		                END), 0) as rt
		FROM
		    sea_import_bl_container bc
		    LEFT JOIN sea_import_bl_container sibc ON bc.job_file_id = sibc.job_file_id AND bc.bl_id = sibc.bl_id
		    LEFT JOIN sea_import_container sic ON sibc.job_file_id = sic.job_file_id AND sibc.line_no = sic.line_no
		    LEFT JOIN container_size cs ON sic.size_id = cs.id
		WHERE
		    bc.is_cfs = 0
		GROUP BY
		    bc.job_file_id, bc.bl_id
        UNION ALL
        SELECT
            bc.job_file_id,
            bc.bl_id,
            maxvalue(coalesce(bc.cbm,0), coalesce(bc.kgs,0)/1000, 1) AS rt
        FROM
            sea_import_bl_container bc
            LEFT JOIN sea_import_bl sib ON bc.job_file_id = sib.job_file_id AND bc.bl_id = sib.bl_id
        WHERE
            bc.is_cfs = 1
            AND sib.is_valid = 1
    ),
    filtered_charges AS (
        SELECT
            inv_cn_id,
            line_no,
            local_amount
        FROM
            inv_cn_charge
            JOIN filtered_inv_cn USING (inv_cn_id)
    ),
    all_bls AS (
        SELECT
            fc.inv_cn_id,
            fc.line_no,
            fc.local_amount,
            b.job_file_id,
            b.bl_id
        FROM
            filtered_charges fc
            JOIN inv_cn_charge_sea_import_bl b ON fc.inv_cn_id = b.inv_cn_id
                AND fc.line_no = b.line_no

        UNION ALL

        SELECT
            fc.inv_cn_id,
            fc.line_no,
            fc.local_amount,
            bc.job_file_id,
            bc.bl_id
        FROM
            filtered_charges fc
            JOIN inv_cn_charge_sea_import_ctr icc ON fc.inv_cn_id = icc.inv_cn_id
                AND fc.line_no = icc.line_no
            JOIN sea_import_bl_container bc ON icc.job_file_id = bc.job_file_id
                AND icc.container_id = bc.line_no

        UNION ALL

        SELECT
            fc.inv_cn_id,
            fc.line_no,
            fc.local_amount,
            ib.job_file_id,
            ib.bl_id
        FROM
            filtered_charges fc
            JOIN inv_cn_sea_import_bl ib ON fc.inv_cn_id = ib.inv_cn_id
            LEFT JOIN inv_cn_charge_sea_import_bl icb ON fc.inv_cn_id = icb.inv_cn_id
                AND fc.line_no = icb.line_no
            LEFT JOIN inv_cn_charge_sea_import_ctr icc ON fc.inv_cn_id = icc.inv_cn_id
                AND fc.line_no = icc.line_no
        WHERE
            icb.inv_cn_id IS NULL
            AND icc.inv_cn_id IS NULL
    )
    SELECT
        ab.inv_cn_id,
        ab.line_no,
        ab.local_amount,
        CAST(ab.job_file_id AS VARCHAR(50)) || '_' || CAST(ab.bl_id AS VARCHAR(50)) as jb_id,
        MAXVALUE(COALESCE(br.rt, 1000),1) as rt
    FROM
        all_bls ab
        LEFT JOIN bl_rt br ON ab.job_file_id = br.job_file_id
            AND ab.bl_id = br.bl_id
    ORDER BY
        ab.inv_cn_id,
        ab.line_no
'''

SQL_SEA_TRIANGLE_PROFIT = '''
    WITH filtered_inv_cn AS (
        SELECT
            invoice_cost_note.id as inv_cn_id
        FROM
            invoice_cost_note
            JOIN sea_triangle_job_file
                ON invoice_cost_note.job_file_id = sea_triangle_job_file.JOB_file_id
        WHERE
            sea_triangle_job_file.job_date BETWEEN ? AND ?
            AND invoice_cost_note.is_valid = 1
            AND invoice_cost_note.status <> 3
    ),
    bl_rt AS (
        SELECT
            ssb.id as bl_id,
            COALESCE(SUM(CASE
                            WHEN ssbc.is_cfs = 0 AND cs.name LIKE '%20%' THEN 1
                            WHEN ssbc.is_cfs = 0 AND cs.name LIKE '%40%' THEN 2
                            WHEN ssbc.is_cfs = 0 AND cs.name LIKE '%45%' THEN 2
                            ELSE 0
                        END), 0) as rt
        FROM
            sea_switch_bl ssb
            LEFT JOIN sea_switch_bl_container ssbc ON ssbc.bl_id = ssb.id
            LEFT JOIN sea_triangle_container stc ON ssbc.CONTAINER_ID = stc.ID
            LEFT JOIN container_size cs ON stc.SIZE_ID = cs.id
        GROUP BY
            ssb.id
        UNION ALL
        SELECT
            ssb.ID as bl_id,
            maxvalue(coalesce(ssbc.cbm,0), coalesce(ssbc.kgs,0)/1000, 1) AS rt
        FROM
            sea_switch_bl ssb
            LEFT JOIN SEA_SWITCH_BL_CONTAINER ssbc ON ssb.id = ssbc.BL_ID
        WHERE
            ssbc.is_cfs = 1
    ),
    filtered_charges AS (
        SELECT
            inv_cn_id,
            line_no,
            local_amount
        FROM
            inv_cn_charge
            JOIN filtered_inv_cn USING (inv_cn_id)
    ),
    all_bls AS (
        SELECT
            fc.inv_cn_id,
            fc.line_no,
            fc.local_amount,
            icsb.bl_id
        FROM
            filtered_charges fc
            JOIN inv_cn_charge_sea_switch_bl icsb ON fc.inv_cn_id = icsb.inv_cn_id
                AND fc.line_no = icsb.line_no
        UNION ALL
        SELECT
            fc.inv_cn_id,
            fc.line_no,
            fc.local_amount,
            ssbc.bl_id
        FROM
            filtered_charges fc
            JOIN inv_cn_charge_sea_switch_ctr icssc ON fc.inv_cn_id = icssc.inv_cn_id
                AND fc.line_no = icssc.line_no
            JOIN sea_switch_bl_container ssbc ON icssc.container_id = ssbc.container_id
        UNION ALL
        SELECT
            fc.inv_cn_id,
            fc.line_no,
            fc.local_amount,
            issb.bl_id
        FROM
            filtered_charges fc
            JOIN inv_cn_sea_switch_bl issb ON fc.inv_cn_id = issb.inv_cn_id
            LEFT JOIN inv_cn_charge_sea_switch_bl icsb_ex ON fc.inv_cn_id = icsb_ex.inv_cn_id AND fc.line_no = icsb_ex.line_no
            LEFT JOIN inv_cn_charge_sea_switch_ctr icc_ex ON fc.inv_cn_id = icc_ex.inv_cn_id AND fc.line_no = icc_ex.line_no
        WHERE
            icsb_ex.inv_cn_id IS NULL
            AND icc_ex.inv_cn_id IS NULL
    )
    SELECT
        ab.inv_cn_id,
        ab.line_no,
        ab.local_amount,
        CAST(ab.bl_id AS VARCHAR(50)) as jb_id,
        MAXVALUE(COALESCE(br.rt, 1000),1) as rt
    FROM
        all_bls ab
        LEFT JOIN bl_rt br ON ab.bl_id = br.bl_id
    WHERE ab.bl_id IS NOT NULL
    ORDER BY
        ab.inv_cn_id,
        ab.line_no
'''

SQL_AIR_PROFIT = '''
    WITH filtered_inv_cn AS (
        SELECT
            invoice_cost_note.id as inv_cn_id
        FROM
            invoice_cost_note
            JOIN air_job_file
                ON invoice_cost_note.job_file_id = air_job_file.JOB_file_id
        WHERE
            air_job_file.flight_date BETWEEN ? AND ?
            AND invoice_cost_note.is_valid = 1
            AND invoice_cost_note.status <> 3
    ),
    awb_rt AS (
        SELECT
            air_waybill.id as awb_id,
            air_waybill.job_file_id,
            COALESCE(air_waybill.chargeable_kgs, air_waybill.gross_kgs, 1.0) AS rt
        FROM
            air_waybill
    ),
    filtered_charges AS (
        SELECT
            inv_cn_id,
            line_no,
            local_amount
        FROM
            inv_cn_charge
            JOIN filtered_inv_cn USING (inv_cn_id)
    ),
    all_awbs AS (
        SELECT
            fc.inv_cn_id,
            fc.line_no,
            fc.local_amount,
            icawb.bl_id as awb_id
        FROM
            filtered_charges fc
            JOIN inv_cn_charge_air_waybill icawb ON fc.inv_cn_id = icawb.inv_cn_id
                AND fc.line_no = icawb.line_no
        UNION ALL
        SELECT
            fc.inv_cn_id,
            fc.line_no,
            fc.local_amount,
            iawb.bl_id as awb_id
        FROM
            filtered_charges fc
            JOIN inv_cn_air_waybill iawb ON fc.inv_cn_id = iawb.inv_cn_id
            LEFT JOIN inv_cn_charge_air_waybill icawb_ex ON fc.inv_cn_id = icawb_ex.inv_cn_id AND fc.line_no = icawb_ex.line_no
        WHERE
            icawb_ex.inv_cn_id IS NULL
    )
    SELECT
        ab.inv_cn_id,
        ab.line_no,
        ab.local_amount,
        CAST(ab.awb_id AS VARCHAR(50)) as jb_id,
        MAXVALUE(COALESCE(ar.rt, 1.0),1) as rt
    FROM
        all_awbs ab
        LEFT JOIN awb_rt ar ON ab.awb_id = ar.awb_id
    WHERE ab.awb_id IS NOT NULL
    ORDER BY
        ab.inv_cn_id,
        ab.line_no
'''

# 工作档明细查询SQL - 简化版本，避免复杂子查询
SQL_JOB_SEA_EXPORT_DETAILS = '''
    SELECT DISTINCT
        sejf.job_file_id,
        jf.job_file_no,
        jf.type_id,
        jf.operator_id,
        operator.full_name as operator_name,
        jf.is_checked,
        jf.is_op_finished,
        sejf.handling_agent_code as job_handling_agent_code,
        job_handling_agent.name as job_handling_agent_name,
        ses.vessel,
        ses.voyage,
        ses.etd_date,
        seps.eta_date,
        ses.pol_code as pol_code,
        seps.pod_code as pod_code,
        ud.name as operator_dept_name,
        icn.job_date
    FROM
        sea_export_job_file sejf
        JOIN job_file jf ON sejf.job_file_id = jf.id
        JOIN invoice_cost_note icn ON jf.id = icn.job_file_id
        JOIN sea_export_sailing ses ON sejf.sailing_id = ses.id
        JOIN sea_export_schedule seps ON sejf.sailing_id = seps.sailing_id AND sejf.sailing_line_no = seps.line_no
        JOIN users operator ON jf.operator_id = operator.user_id
        LEFT JOIN users_department ud ON operator.dept_id = ud.dept_id
        LEFT JOIN company job_handling_agent ON sejf.handling_agent_code = job_handling_agent.code
    WHERE
        icn.job_date BETWEEN ? AND ? AND jf.is_active = 1 AND icn.is_valid = 1
    ORDER BY sejf.job_file_id
'''

SQL_JOB_SEA_IMPORT_DETAILS = '''
    SELECT DISTINCT
        sijf.job_file_id,
        jf.job_file_no,
        jf.type_id,
        jf.operator_id,
        operator.full_name as operator_name,
        jf.is_checked,
        jf.is_op_finished,
        sijf.agent_code as job_handling_agent_code,
        handling_agent.name as job_handling_agent_name,
        sijf.vessel,
        sijf.voyage,
        sijf.eta_date as etd_date,  -- 进口用ETA作为主要日期
        sijf.eta_date,
        sijf.pol_code as pol_code,
        NULL as pod_code,  -- 海运进口没有pod_code字段
        ud.name as operator_dept_name,
        icn.job_date
    FROM
        sea_import_job_file sijf
        JOIN job_file jf ON sijf.job_file_id = jf.id
        JOIN invoice_cost_note icn ON jf.id = icn.job_file_id
        JOIN users operator ON jf.operator_id = operator.user_id
        LEFT JOIN company handling_agent ON sijf.agent_code = handling_agent.code
        LEFT JOIN users_department ud ON operator.dept_id = ud.dept_id
    WHERE
        icn.job_date BETWEEN ? AND ? AND jf.is_active = 1 AND icn.is_valid = 1
    ORDER BY sijf.job_file_id
'''

SQL_JOB_SEA_TRIANGLE_DETAILS = '''
    SELECT DISTINCT
        stjf.job_file_id,
        jf.job_file_no,
        jf.type_id,
        jf.operator_id,
        operator.full_name as operator_name,
        jf.is_checked,
        jf.is_op_finished,
        stjf.pod_agent_code as job_handling_agent_code,
        pod_agent.name as job_handling_agent_name,
        stjf.vessel,
        stjf.voyage,
        stjf.etd_date,
        stjf.eta_date,
        stjf.pol_code as pol_code,
        stjf.pod_code as pod_code,
        ud.name as operator_dept_name,
        stjf.job_date  -- 三角贸易使用job表中的job_date
    FROM
        sea_triangle_job_file stjf
        JOIN job_file jf ON stjf.job_file_id = jf.id
        JOIN users operator ON jf.operator_id = operator.user_id
        LEFT JOIN company pod_agent ON stjf.pod_agent_code = pod_agent.code
        LEFT JOIN users_department ud ON operator.dept_id = ud.dept_id
    WHERE
        stjf.job_date BETWEEN ? AND ? AND jf.is_active = 1
    ORDER BY stjf.job_file_id
'''

SQL_JOB_AIR_DETAILS = '''
    SELECT DISTINCT
        ajf.job_file_id,
        jf.job_file_no,
        jf.type_id,
        jf.operator_id,
        operator.full_name as operator_name,
        jf.is_checked,
        jf.is_op_finished,
        ajf.agent_code as job_handling_agent_code,
        handling_agent.name as job_handling_agent_name,
        ajf.flight_no as vessel,
        CAST(ajf.flight_date AS VARCHAR(30)) as voyage,
        ajf.flight_date as etd_date,
        ajf.arrival_date as eta_date,
        ajf.departure as pol_code,
        ajf.destination as pod_code,
        ud.name as operator_dept_name,
        icn.job_date
    FROM
        air_job_file ajf
        JOIN job_file jf ON ajf.job_file_id = jf.id
        JOIN invoice_cost_note icn ON jf.id = icn.job_file_id
        JOIN users operator ON jf.operator_id = operator.user_id
        LEFT JOIN company handling_agent ON ajf.agent_code = handling_agent.code
        LEFT JOIN users_department ud ON operator.dept_id = ud.dept_id
    WHERE
        icn.job_date BETWEEN ? AND ? AND jf.is_active = 1 AND icn.is_valid = 1
    ORDER BY ajf.job_file_id
'''

# Booking明细查询SQL - 统一结构，包含转运信息
SQL_SEA_EXPORT_DETAILS = '''
    SELECT
    	DISTINCT
        CAST(seb.id AS VARCHAR(50)) as jb_id,
        seb.id AS entity_id,
        seb.pol_code AS pol_code,
        seb.dest_port_code AS pod_code,
        seb.service_mode,
        s.name AS shipper_name,
        na.name AS nomi_agent_name,
        us.full_name AS salesman_name,
        usd.dept_id AS salesman_dept_id,
        usd.name AS salesman_dept_name,
        uo.full_name AS operator_name,
        uod.name AS operator_dept_name,
        jf.job_file_no,
        ses.vessel,
        ses.voyage,
        ses.etd_date,
        ses.pol_code AS sailing_pol,
        seb.is_free_hand,
        jf.type_id,
        jf.id as job_file_id,
        jf.operator_id,
        jha.name as job_handling_agent_name,
        ha.name as handling_agent_name,
        seb.handling_agent_code as handling_agent_code,
        coloader.code as coloader_code,
        coloader.name as coloader_name,
        jf.is_checked,
        jf.is_op_finished,
        CASE WHEN tseb.bk_id IS NOT NULL THEN 1 ELSE 0 END as is_transhipment,
        tseb.transhipment_id,
        NULL as business_no,
        seps.eta_date,
        icn.job_date,
        CAST(COALESCE((SELECT MAX(maxvalue(COALESCE(sebct.cbm, 0), COALESCE(sebct.kgs, 0)/1000, 1))
                       FROM sea_export_bk_cfs_tonnage sebct
                       WHERE sebct.bk_id = seb.id), 0) AS DECIMAL(15,3)) as lcl_rt,
        CAST(COALESCE((SELECT SUM(CASE
                                        WHEN cs.name LIKE '%20%' THEN sebc.quantity
                                        WHEN cs.name LIKE '%40%' THEN sebc.quantity * 2
                                        WHEN cs.name LIKE '%45%' THEN sebc.quantity * 2
                                        ELSE 0
                                     END)
                       FROM sea_export_bk_container sebc
                       LEFT JOIN container_size cs ON sebc.size_id = cs.id
                       WHERE sebc.bk_id = seb.id
                       AND sebc.quantity > 0), 0) AS DECIMAL(15,3)) as teu,
        CAST(NULL AS DECIMAL(15,3)) as air_weight
    FROM
        sea_export_booking seb
        INNER JOIN sea_export_job_file sejf ON seb.job_file_id = sejf.job_file_id
        INNER JOIN invoice_cost_note icn ON sejf.job_file_id = icn.job_file_id
        LEFT JOIN sea_export_sailing ses ON sejf.sailing_id = ses.id
        LEFT JOIN sea_export_schedule seps ON sejf.sailing_id = seps.sailing_id AND sejf.sailing_line_no = seps.line_no AND seps.pod_code = seb.dest_port_code
        INNER JOIN job_file jf ON sejf.job_file_id = jf.id
        LEFT JOIN users AS us ON seb.salesman_id = us.user_id
        LEFT JOIN users_department AS usd ON us.dept_id = usd.dept_id
        LEFT JOIN company as jha ON sejf.handling_agent_code = jha.code
        LEFT JOIN company as s ON seb.shipper_code = s.code
        LEFT JOIN company as na ON seb.nomination_agent_code = na.code
        LEFT JOIN company as ha ON seb.handling_agent_code = ha.code
        LEFT JOIN company as coloader ON seb.coloader_code = coloader.code
        INNER JOIN users AS uo ON jf.operator_id = uo.user_id
        LEFT JOIN users_department AS uod ON uo.dept_id = uod.dept_id
        LEFT JOIN transhipment_sea_export_bk tseb ON seb.id = tseb.bk_id
    WHERE icn.job_date BETWEEN ? AND ?
    AND seb.is_valid = 1
    ORDER BY seb.id
'''

# 性能优化版本的查询 - 移除复杂的子查询字段
SQL_SEA_EXPORT_DETAILS_OPTIMIZED = '''
    SELECT
        DISTINCT
        CAST(seb.id AS VARCHAR(50)) as jb_id,
        seb.id AS entity_id,
        seb.pol_code AS pol_code,
        seb.dest_port_code AS pod_code,
        seb.service_mode,
        s.name AS shipper_name,
        na.name AS nomi_agent_name,
        us.full_name AS salesman_name,
        usd.dept_id AS salesman_dept_id,
        usd.name AS salesman_dept_name,
        uo.full_name AS operator_name,
        uod.name AS operator_dept_name,
        jf.job_file_no,
        ses.vessel,
        ses.voyage,
        ses.etd_date,
        ses.pol_code AS sailing_pol,
        seb.is_free_hand,
        jf.type_id,
        jf.id as job_file_id,
        jf.operator_id,
        jha.name as job_handling_agent_name,
        ha.name as handling_agent_name,
        seb.handling_agent_code as handling_agent_code,
        coloader.code as coloader_code,
        coloader.name as coloader_name,
        jf.is_checked,
        jf.is_op_finished,
        CASE WHEN tseb.bk_id IS NOT NULL THEN 1 ELSE 0 END as is_transhipment,
        tseb.transhipment_id,
        NULL as business_no,
        seps.eta_date,
        icn.job_date,
        -- 优化：保留必要的子查询计算，但简化逻辑
        CAST(COALESCE((SELECT MAX(maxvalue(COALESCE(sebct.cbm, 0), COALESCE(sebct.kgs, 0)/1000, 1))
                       FROM sea_export_bk_cfs_tonnage sebct
                       WHERE sebct.bk_id = seb.id), 0) AS DECIMAL(15,3)) as lcl_rt,
        CAST(COALESCE((SELECT SUM(CASE
                                        WHEN cs.name LIKE '%20%' THEN sebc.quantity
                                        WHEN cs.name LIKE '%40%' THEN sebc.quantity * 2
                                        WHEN cs.name LIKE '%45%' THEN sebc.quantity * 2
                                        ELSE 0
                                     END)
                       FROM sea_export_bk_container sebc
                       LEFT JOIN container_size cs ON sebc.size_id = cs.id
                       WHERE sebc.bk_id = seb.id
                       AND sebc.quantity > 0), 0) AS DECIMAL(15,3)) as teu,
        CAST(NULL AS DECIMAL(15,3)) as air_weight
    FROM
        sea_export_booking seb
        INNER JOIN sea_export_job_file sejf ON seb.job_file_id = sejf.job_file_id
        INNER JOIN invoice_cost_note icn ON sejf.job_file_id = icn.job_file_id
        INNER JOIN job_file jf ON sejf.job_file_id = jf.id
        -- 优化：使用INNER JOIN替代LEFT JOIN（如果可能）
        INNER JOIN users AS us ON seb.salesman_id = us.user_id
        INNER JOIN users_department AS usd ON us.dept_id = usd.dept_id
        INNER JOIN users AS uo ON jf.operator_id = uo.user_id
        INNER JOIN users_department AS uod ON uo.dept_id = uod.dept_id
        -- 保留必要的LEFT JOIN
        LEFT JOIN sea_export_sailing ses ON sejf.sailing_id = ses.id
        LEFT JOIN sea_export_schedule seps ON sejf.sailing_id = seps.sailing_id 
            AND sejf.sailing_line_no = seps.line_no 
            AND seps.pod_code = seb.dest_port_code
        LEFT JOIN company as jha ON sejf.handling_agent_code = jha.code
        LEFT JOIN company as s ON seb.shipper_code = s.code
        LEFT JOIN company as na ON seb.nomination_agent_code = na.code
        LEFT JOIN company as ha ON seb.handling_agent_code = ha.code
        LEFT JOIN company as coloader ON seb.coloader_code = coloader.code
        LEFT JOIN transhipment_sea_export_bk tseb ON seb.id = tseb.bk_id
    WHERE icn.job_date BETWEEN ? AND ?
        AND seb.is_valid = 1
        AND icn.is_valid = 1
        AND jf.is_active = 1
    ORDER BY seb.id
'''

SQL_SEA_IMPORT_DETAILS = '''
    SELECT
    	DISTINCT
        CAST(sib.job_file_id AS VARCHAR(50)) || '_' || CAST(sib.bl_id AS VARCHAR(50)) as jb_id,
        CAST(sib.job_file_id AS VARCHAR(50)) || '_' || CAST(sib.bl_id AS VARCHAR(50)) as entity_id,
        sijf.pol_code AS pol_code,
        NULL AS pod_code,
        CASE
            WHEN UPPER(TRIM(sib.service_mode_from)) = 'CY' AND UPPER(TRIM(sib.service_mode_to)) = 'CY' THEN 2
            WHEN UPPER(TRIM(sib.service_mode_from)) = 'CFS' AND UPPER(TRIM(sib.service_mode_to)) = 'CFS' THEN 1
            WHEN UPPER(TRIM(sib.service_mode_from)) = 'CFS' AND UPPER(TRIM(sib.service_mode_to)) = 'CY' THEN 3
            ELSE 0
        END as service_mode,
        s.name AS shipper_name,
        na.name AS nomi_agent_name,
        us.full_name AS salesman_name,
        usd.dept_id AS salesman_dept_id,
        usd.name AS salesman_dept_name,
        uo.full_name AS operator_name,
        uod.name AS operator_dept_name,
        jf.job_file_no,
        sijf.vessel,
        sijf.voyage,
        sijf.etd_date,
        sijf.pol_code AS sailing_pol,
        sib.is_free_hand,
        jf.type_id,
        jf.id as job_file_id,
        jf.operator_id,
        jha.name as job_handling_agent_name,
        ha.name as handling_agent_name,
        sib.handling_agent_code as handling_agent_code,
        NULL as coloader_code,
        NULL as coloader_name,
        jf.is_checked,
        jf.is_op_finished,
        CASE WHEN tsib.job_file_id IS NOT NULL THEN 1 ELSE 0 END as is_transhipment,
        tsib.transhipment_id,
        NULL as business_no,
        sijf.eta_date,
        icn.job_date,
        CAST(COALESCE((SELECT SUM(maxvalue(coalesce(sibc.cbm,0), coalesce(sibc.kgs,0)/1000, 1))
                       FROM sea_import_bl_container sibc
                       WHERE sibc.job_file_id = sib.job_file_id AND sibc.bl_id = sib.bl_id AND sibc.IS_CFS = 1), 0) AS DECIMAL(15,3)) as lcl_rt,
        CAST(COALESCE((SELECT SUM(CASE
                                        WHEN sibc.is_cfs = 0 AND cs.name LIKE '%20%' THEN 1
                                        WHEN sibc.is_cfs = 0 AND cs.name LIKE '%40%' THEN 2
                                        WHEN sibc.is_cfs = 0 AND cs.name LIKE '%45%' THEN 2
                                        ELSE 0
                                     END)
                       FROM sea_import_bl_container sibc
                       LEFT JOIN sea_import_container sic ON sibc.JOB_FILE_ID = sic.job_file_id AND sibc.LINE_NO = sic.line_no
                       LEFT JOIN container_size cs ON sic.SIZE_ID = cs.id
                       WHERE sibc.job_file_id = sib.job_file_id AND sibc.bl_id = sib.bl_id), 0) AS DECIMAL(15,3)) as teu,
        CAST(NULL AS DECIMAL(15,3)) as air_weight
    FROM
        sea_import_bl sib
        INNER JOIN sea_import_job_file sijf ON sib.job_file_id = sijf.job_file_id
        INNER JOIN invoice_cost_note icn ON sijf.job_file_id = icn.job_file_id
        INNER JOIN job_file jf ON sijf.job_file_id = jf.id
        LEFT JOIN users AS us ON sib.salesman_id = us.user_id
        LEFT JOIN users_department AS usd ON us.dept_id = usd.dept_id
        LEFT JOIN company as jha ON sijf.agent_code = jha.code
        LEFT JOIN company as s ON sib.shipper_code = s.code
        LEFT JOIN company as na ON sib.nomination_agent_code = na.code
        LEFT JOIN company as ha ON sib.handling_agent_code = ha.code
        INNER JOIN users AS uo ON jf.operator_id = uo.user_id
        LEFT JOIN users_department AS uod ON uo.dept_id = uod.dept_id
        LEFT JOIN transhipment_sea_import_bl tsib ON sib.job_file_id = tsib.job_file_id AND sib.bl_id = tsib.bl_id
    WHERE icn.job_date BETWEEN ? AND ?
    AND sib.is_valid = 1
    ORDER BY sib.job_file_id, sib.bl_id
'''

SQL_SEA_TRIANGLE_DETAILS = '''
    SELECT
    	DISTINCT
        CAST(ssb.id AS VARCHAR(50)) as jb_id,
        ssb.id AS entity_id,
        stjf.pol_code AS pol_code,
        stjf.pod_code AS pod_code,
        CASE
            WHEN UPPER(TRIM(ssb.service_mode_from)) = 'CY' AND UPPER(TRIM(ssb.service_mode_to)) = 'CY' THEN 2
            WHEN UPPER(TRIM(ssb.service_mode_from)) = 'CFS' AND UPPER(TRIM(ssb.service_mode_to)) = 'CFS' THEN 1
            WHEN UPPER(TRIM(ssb.service_mode_from)) = 'CFS' AND UPPER(TRIM(ssb.service_mode_to)) = 'CY' THEN 3
            ELSE 0
        END as service_mode,
        s.name AS shipper_name,
        na.name AS nomi_agent_name,
        us.full_name AS salesman_name,
        usd.dept_id AS salesman_dept_id,
        usd.name AS salesman_dept_name,
        uo.full_name AS operator_name,
        uod.name AS operator_dept_name,
        jf.job_file_no,
        stjf.vessel,
        stjf.voyage,
        stjf.etd_date,
        stjf.pol_code AS sailing_pol,
        ssb.is_free_hand,
        jf.type_id,
        jf.id as job_file_id,
        jf.operator_id,
        jha.name as job_handling_agent_name,
        ha.name as handling_agent_name,
        ssb.handling_agent_code as handling_agent_code,
        NULL as coloader_code,
        NULL as coloader_name,
        jf.is_checked,
        jf.is_op_finished,
        CASE WHEN tssb.bl_id IS NOT NULL THEN 1 ELSE 0 END as is_transhipment,
        tssb.transhipment_id,
        NULL as business_no,
        stjf.eta_date,
        stjf.job_date,
        CAST(COALESCE((SELECT SUM(MAXVALUE(COALESCE(ssbc.cbm, 0), COALESCE(ssbc.kgs, 0) / 1000, 1))
                       FROM sea_switch_bl_container ssbc
                       WHERE ssbc.bl_id = ssb.id), 0) AS DECIMAL(15,3)) as lcl_rt,
        CAST(COALESCE((SELECT SUM(CASE
                                        WHEN ssbc.is_cfs = 0 AND cs.name LIKE '%20%' THEN 1
                                        WHEN ssbc.is_cfs = 0 AND cs.name LIKE '%40%' THEN 2
                                        WHEN ssbc.is_cfs = 0 AND cs.name LIKE '%45%' THEN 2
                                        ELSE 0
                                     END)
                       FROM sea_switch_bl_container ssbc
                       LEFT JOIN sea_triangle_container stc ON ssbc.CONTAINER_ID  = stc.ID
                       LEFT JOIN container_size cs ON stc.SIZE_ID = cs.id
                       WHERE ssbc.bl_id = ssb.id), 0) AS DECIMAL(15,3)) as teu,
        CAST(NULL AS DECIMAL(15,3)) as air_weight
    FROM
        sea_switch_bl ssb
        INNER JOIN SEA_TRIANGLE_JOB_FILE stjf ON ssb.job_file_id = stjf.job_file_id
        INNER JOIN job_file jf ON stjf.job_file_id = jf.id
        LEFT JOIN users AS us ON ssb.salesman_id = us.user_id
        LEFT JOIN users_department AS usd ON us.dept_id = usd.dept_id
        LEFT JOIN company as jha ON stjf.pod_agent_code = jha.code
        LEFT JOIN company as s ON ssb.shipper_code = s.code
        LEFT JOIN company as na ON ssb.nomination_agent_code = na.code
        LEFT JOIN company as ha ON ssb.handling_agent_code = ha.code
        INNER JOIN users AS uo ON jf.operator_id = uo.user_id
        LEFT JOIN users_department AS uod ON uo.dept_id = uod.dept_id
        LEFT JOIN transhipment_sea_switch_bl tssb ON ssb.id = tssb.bl_id
    WHERE stjf.job_date BETWEEN ? AND ?
    AND ssb.is_valid = 1
    ORDER BY ssb.id
'''

SQL_AIR_DETAILS = '''
    SELECT
    	DISTINCT
        CAST(awb.id AS VARCHAR(50)) as jb_id,
        awb.id AS entity_id,
        ajf.departure AS pol_code,
        ajf.destination AS pod_code,
        4 as service_mode,
        s.name AS shipper_name,
        na.name AS nomi_agent_name,
        us.full_name AS salesman_name,
        usd.dept_id AS salesman_dept_id,
        usd.name AS salesman_dept_name,
        uo.full_name AS operator_name,
        uod.name AS operator_dept_name,
        jf.job_file_no,
        COALESCE(ajf.flight_no, NULL) as vessel,
        COALESCE(ajf.flight_date, NULL) as voyage,
        ajf.atd_date as etd_date,
        ajf.departure AS sailing_pol,
        awb.is_free_hand,
        jf.type_id,
        jf.id as job_file_id,
        jf.operator_id,
        jha.name as job_handling_agent_name,
        jha.name as handling_agent_name,
        ajf.agent_code as handling_agent_code,
        ajf.coloader_code as coloader_code,
        coloader.name as coloader_name,
        jf.is_checked,
        jf.is_op_finished,
        CASE WHEN taw.bl_id IS NOT NULL THEN 1 ELSE 0 END as is_transhipment,
        taw.transhipment_id,
        NULL as business_no,
        ajf.arrival_date as eta_date,
        icn.job_date,
        CAST(0 AS DECIMAL(15,3)) as lcl_rt,
        CAST(0 AS DECIMAL(15,3)) as teu,
        CAST(COALESCE(awb.chargeable_kgs, awb.gross_kgs, 1.0) AS DECIMAL(15,3)) as air_weight
    FROM
        air_waybill awb
        INNER JOIN air_job_file ajf ON awb.job_file_id = ajf.job_file_id
        INNER JOIN invoice_cost_note icn ON ajf.job_file_id = icn.job_file_id
        INNER JOIN job_file jf ON ajf.job_file_id = jf.id
        LEFT JOIN users AS us ON awb.salesman_id = us.user_id
        LEFT JOIN users_department AS usd ON us.dept_id = usd.dept_id
        LEFT JOIN company as jha ON ajf.agent_code = jha.code
        LEFT JOIN company as s ON awb.shipper_code = s.code
        LEFT JOIN company as na ON awb.nomination_agent_code = na.code
        LEFT JOIN company as coloader ON ajf.coloader_code = coloader.code
        INNER JOIN users AS uo ON jf.operator_id = uo.user_id
        LEFT JOIN users_department AS uod ON uo.dept_id = uod.dept_id
        LEFT JOIN transhipment_air_waybill taw ON awb.id = taw.bl_id
    WHERE icn.job_date BETWEEN ? AND ?
    AND awb.is_valid = 1
    ORDER BY awb.id
'''

# 辅助函数
def calculate_profit_from_charges_unified(
    results: List[Tuple],
    business_type: str,
    logger_prefix: str = ""
) -> pd.DataFrame:
    """
    统一的profit计算函数，使用SQL中直接构造的jb_id，并包含转运信息

    Args:
        results: SQL查询结果，格式为 (inv_cn_id, line_no, local_amount, jb_id, rt)
        business_type: 业务类型 ('sea_export', 'sea_import', 'sea_triangle', 'air')
        logger_prefix: 日志前缀

    Returns:
        处理后的DataFrame，包含jb_id, rt, income, cost, profit, is_transhipment, transhipment_id
    """
    if not results:
        logger.info(f"{logger_prefix} No results to process, returning empty DataFrame.")
        return pd.DataFrame()

    logger.info(f"{logger_prefix} Converting {len(results)} results to DataFrame.")

    # 创建DataFrame，列名固定为SQL返回的格式
    df = pd.DataFrame(results, columns=['inv_cn_id', 'line_no', 'local_amount', 'jb_id', 'rt'])
    logger.info(f"{logger_prefix} DataFrame created. Shape: {df.shape if not df.empty else 'Empty'}. Performing type conversion.")

    # 数据类型转换
    df = df.astype({
        'local_amount': 'float',
        'rt': 'float',
        'inv_cn_id': 'int',
        'line_no': 'int',
        'jb_id': 'str'
    })
    logger.info(f"{logger_prefix} Type conversion complete. Calculating proportion and income/cost.")

    # 计算比例分配
    group_sum_rt = df.groupby(['inv_cn_id', 'line_no'])['rt'].transform('sum')
    logger.info(f"{logger_prefix} Calculated group_sum_rt for proportion calculation.")

    # 直接计算income和cost，不创建中间的proportion列
    proportion_calculation = (
        (df['rt'] / group_sum_rt.replace(0, pd.NA)) * df['local_amount']
    )
    proportion_values = pd.to_numeric(proportion_calculation, errors='coerce').fillna(0)

    # 基于比例值计算income和cost
    df['income'] = proportion_values.apply(lambda x: max(x, 0))
    df['cost'] = proportion_values.apply(lambda x: min(x, 0))
    logger.info(f"{logger_prefix} Income/cost calculation complete. Aggregating by jb_id.")

    # 按jb_id聚合
    df_profit = df.groupby(['jb_id']).agg({
        'rt': 'sum',
        'income': 'sum',
        'cost': 'sum'
    }).reset_index()

    logger.info(f"{logger_prefix} Aggregation complete. Shape: {df_profit.shape if not df_profit.empty else 'Empty'}.")

    # 计算profit
    df_profit['profit'] = df_profit['income'] + df_profit['cost']
    logger.info(f"{logger_prefix} Profit column calculated. Rounding numerical columns.")

    # 数值精度设置
    for col in ['rt', 'income', 'cost', 'profit']:
        df_profit[col] = df_profit[col].round(2)

    logger.info(f"{logger_prefix} Numerical columns rounded. Querying transhipment information.")

    return df_profit

def query_business_numbers_gbk(begin_date: str, end_date: str, business_type: str, logger_prefix: str = "") -> Dict[str, str]:
    """
    通过job_date时间范围查询GBK编码的业务号码字段

    Args:
        begin_date: 开始日期
        end_date: 结束日期
        business_type: 业务类型 ('sea_export', 'sea_import', 'sea_triangle', 'air')
        logger_prefix: 日志前缀

    Returns:
        Dict[entity_id, business_no]: 实体ID到业务号码的映射
    """
    if not begin_date or not end_date:
        logger.info(f"{logger_prefix} No date range provided for business numbers query.")
        return {}

    # 根据业务类型选择对应的SQL（通过job_date过滤）
    sql_map = {
        'sea_export': '''
            SELECT CAST(seb.id AS VARCHAR(50)) as entity_id, seb.booking_no as business_no
            FROM sea_export_booking seb
            INNER JOIN sea_export_job_file sejf ON seb.job_file_id = sejf.job_file_id
            INNER JOIN invoice_cost_note icn ON sejf.job_file_id = icn.job_file_id
            WHERE icn.job_date BETWEEN ? AND ?
        ''',
        'sea_import': '''
            SELECT CAST(sib.job_file_id AS VARCHAR(50)) || '_' || CAST(sib.bl_id AS VARCHAR(50)) as entity_id,
                   sib.bl_no as business_no
            FROM sea_import_bl sib
            INNER JOIN sea_import_job_file sijf ON sib.job_file_id = sijf.job_file_id
            INNER JOIN invoice_cost_note icn ON sijf.job_file_id = icn.job_file_id
            WHERE icn.job_date BETWEEN ? AND ?
        ''',
        'sea_triangle': '''
            SELECT CAST(ssb.id AS VARCHAR(50)) as entity_id, ssb.bl_no as business_no
            FROM sea_switch_bl ssb
            INNER JOIN sea_triangle_job_file stjf ON ssb.job_file_id = stjf.job_file_id
            INNER JOIN invoice_cost_note icn ON stjf.job_file_id = icn.job_file_id
            WHERE icn.job_date BETWEEN ? AND ?
        ''',
        'air': '''
            SELECT CAST(awb.id AS VARCHAR(50)) as entity_id, awb.house_no as business_no
            FROM air_waybill awb
            INNER JOIN air_job_file ajf ON awb.job_file_id = ajf.job_file_id
            INNER JOIN invoice_cost_note icn ON ajf.job_file_id = icn.job_file_id
            WHERE icn.job_date BETWEEN ? AND ?
        '''
    }

    if business_type not in sql_map:
        logger.error(f"{logger_prefix} Unknown business_type: {business_type}")
        return {}

    sql = sql_map[business_type]
    results = {}

    try:
        with get_pooled_pro2_connection(charset_preference='GBK') as conn:
            with conn.cursor() as cursor:
                logger.info(f"{logger_prefix} Querying {business_type} business numbers for date range {begin_date} to {end_date}")
                cursor.execute(sql, (begin_date, end_date))
                rows = cursor.fetchall()

                for entity_id, business_no in rows:
                    # 处理GBK编码
                    if business_no:
                        try:
                            # 如果business_no是bytes，尝试用GBK解码
                            if isinstance(business_no, bytes):
                                business_no = business_no.decode('gbk', errors='ignore')
                            # 如果是字符串，尝试重新编码处理
                            elif isinstance(business_no, str):
                                # 先编码为latin-1再解码为gbk（处理可能的编码问题）
                                try:
                                    business_no = business_no.encode('latin-1').decode('gbk', errors='ignore')
                                except (UnicodeEncodeError, UnicodeDecodeError):
                                    # 如果转换失败，保持原值
                                    pass
                        except (UnicodeDecodeError, AttributeError):
                            # 如果解码失败，保持原值
                            pass

                    results[entity_id] = business_no

                logger.info(f"{logger_prefix} Successfully queried {len(results)} {business_type} business number records")

    except Exception as e:
        logger.error(f"{logger_prefix} Error querying business numbers: {e}", exc_info=True)
        raise DatabaseError(f"查询业务号码失败: {e}")

    return results

# 按日期范围查询Booking级别的业务详情数据（包含业务号码和利润数据）
def query_business_details_by_date(begin_date: str, end_date: str, business_type: str = 'all', include_business_no: bool = True, include_profit: bool = True, logger_prefix: str = "") -> List[Dict]:
    """
    按日期范围查询业务详情数据，可选择是否包含业务号码和利润数据

    Args:
        begin_date: 开始日期
        end_date: 结束日期
        business_type: 业务类型 ('sea_export', 'sea_import', 'sea_triangle', 'air', 'all')，默认'all'表示所有业务类型
        include_business_no: 是否包含业务号码（默认True）
        include_profit: 是否包含利润数据（默认True）
        logger_prefix: 日志前缀

    Returns:
        List[Dict]: 业务详情数据列表（包含income/cost/profit字段和business_type_name字段）
    """
    if not begin_date or not end_date:
        logger.info(f"{logger_prefix} No date range provided.")
        return []

    # 定义所有业务类型
    all_business_types = ['sea_export', 'sea_import', 'sea_triangle', 'air']

    # 确定要查询的业务类型
    if business_type == 'all':
        target_types = all_business_types
        logger.info(f"{logger_prefix} Querying all business types: {target_types}")
    elif business_type in all_business_types:
        target_types = [business_type]
        logger.info(f"{logger_prefix} Querying single business type: {business_type}")
    else:
        logger.error(f"{logger_prefix} Unknown business_type: {business_type}")
        return []

    # 选择对应的SQL（使用按日期查询的版本）
    # 根据优化配置选择查询版本
    if ENABLE_FIELD_OPTIMIZATION:
        sql_map = {
            'sea_export': SQL_SEA_EXPORT_DETAILS_OPTIMIZED,
            'sea_import': SQL_SEA_IMPORT_DETAILS,
            'sea_triangle': SQL_SEA_TRIANGLE_DETAILS,
            'air': SQL_AIR_DETAILS
        }
        logger.info(f"{logger_prefix} Using optimized SQL queries for better performance")
    else:
        sql_map = {
            'sea_export': SQL_SEA_EXPORT_DETAILS,
            'sea_import': SQL_SEA_IMPORT_DETAILS,
            'sea_triangle': SQL_SEA_TRIANGLE_DETAILS,
            'air': SQL_AIR_DETAILS
        }

    # 业务类型名称映射
    business_type_names = {
        'sea_export': '海运出口',
        'sea_import': '海运进口',
        'sea_triangle': '海运三角贸易',
        'air': '空运'
    }

    all_results = []

    # 循环处理每个业务类型
    for current_type in target_types:
        try:
            sql = sql_map[current_type]
            type_results = []

            with get_pooled_pro2_connection() as conn:
                with conn.cursor() as cursor:
                    logger.info(f"{logger_prefix} Executing {current_type} details query for date range {begin_date} to {end_date}")
                    cursor.execute(sql, (begin_date, end_date))
                    rows = cursor.fetchall()

                    # 获取列名
                    column_names = [
                        'jb_id', 'entity_id', 'pol_code', 'pod_code', 'service_mode',
                        'shipper_name', 'nomi_agent_name', 'salesman_name', 'salesman_dept_id', 'salesman_dept_name',
                        'operator_name', 'operator_dept_name', 'job_file_no', 'vessel', 'voyage', 'etd_date',
                        'sailing_pol', 'is_free_hand', 'type_id', 'job_file_id', 'operator_id',
                        'job_handling_agent_name', 'handling_agent_name', 'handling_agent_code',
                        'coloader_code', 'coloader_name', 'is_checked', 'is_op_finished',
                        'is_transhipment', 'transhipment_id',
                        'business_no', 'eta_date', 'job_date',
                        'lcl_rt', 'teu', 'air_weight'
                    ]

                    # 转换为字典列表
                    for row in rows:
                        row_dict = {}
                        for i, value in enumerate(row):
                            if i < len(column_names):
                                row_dict[column_names[i]] = value

                        # 转换数值字段为float类型
                        numeric_fields = ['lcl_rt', 'teu', 'air_weight']
                        for field in numeric_fields:
                            if field in row_dict and row_dict[field] is not None:
                                try:
                                    row_dict[field] = float(row_dict[field])
                                except (ValueError, TypeError):
                                    row_dict[field] = 0.0
                            elif field in row_dict:
                                row_dict[field] = 0.0

                        # 添加业务类型信息
                        row_dict['business_type'] = current_type
                        row_dict['business_type_name'] = business_type_names[current_type]
                        type_results.append(row_dict)

                    logger.info(f"{logger_prefix} Successfully queried {len(type_results)} {current_type} detail records")

            # 如果需要包含业务号码，则查询并合并
            if include_business_no and type_results:
                try:
                    business_numbers = query_business_numbers_gbk(begin_date, end_date, current_type, logger_prefix)

                    # 合并业务号码数据
                    for detail in type_results:
                        # 优先使用jb_id匹配（字符串类型），其次使用entity_id
                        jb_id = detail.get('jb_id')
                        entity_id = detail.get('entity_id')

                        business_no = None
                        if jb_id and str(jb_id) in business_numbers:
                            business_no = business_numbers[str(jb_id)]
                        elif entity_id and str(entity_id) in business_numbers:
                            business_no = business_numbers[str(entity_id)]

                        detail['business_no'] = business_no if business_no else detail.get('business_no', '')

                    logger.info(f"{logger_prefix} Successfully merged business numbers for {len(type_results)} {current_type} records")

                except Exception as e:
                    logger.warning(f"{logger_prefix} Failed to query {current_type} business numbers, using empty values: {e}")
                    # 如果业务号码查询失败，设置为空值
                    for detail in type_results:
                        if 'business_no' not in detail or detail.get('business_no') is None:
                            detail['business_no'] = ''

            # 如果需要包含利润数据，则查询并合并
            if include_profit and type_results:
                try:
                    # 根据业务类型选择对应的利润查询SQL
                    profit_sql_map = {
                        'sea_export': SQL_SEA_EXPORT_PROFIT,
                        'sea_import': SQL_SEA_IMPORT_PROFIT,
                        'sea_triangle': SQL_SEA_TRIANGLE_PROFIT,
                        'air': SQL_AIR_PROFIT
                    }

                    if current_type in profit_sql_map:
                        with get_pooled_pro2_connection() as conn:
                            with conn.cursor() as cursor:
                                logger.info(f"{logger_prefix} Querying {current_type} profit data for date range {begin_date} to {end_date}")
                                cursor.execute(profit_sql_map[current_type], (begin_date, end_date))
                                profit_rows = cursor.fetchall()

                                # 使用统一的profit计算函数
                                df_profit = calculate_profit_from_charges_unified(
                                    results=profit_rows,
                                    business_type=current_type,
                                    logger_prefix=logger_prefix
                                )

                                # 将DataFrame转换为字典，以jb_id为键
                                profit_dict = {}
                                for _, row in df_profit.iterrows():
                                    jb_id = str(row.get('jb_id', ''))
                                    profit_dict[jb_id] = {
                                        'income': float(row.get('income', 0)),
                                        'cost': float(row.get('cost', 0)),
                                        'profit': float(row.get('profit', 0))
                                    }

                                # 合并利润数据到详情数据中
                                for detail in type_results:
                                    jb_id = str(detail.get('jb_id', ''))
                                    if jb_id in profit_dict:
                                        detail.update(profit_dict[jb_id])
                                    else:
                                        detail.update({
                                            'income': 0.0,
                                            'cost': 0.0,
                                            'profit': 0.0
                                        })

                                logger.info(f"{logger_prefix} Successfully merged profit data for {len(type_results)} {current_type} records")

                except Exception as e:
                    logger.warning(f"{logger_prefix} Failed to query {current_type} profit data, using zero values: {e}")
                    # 如果利润数据查询失败，设置为0值
                    for detail in type_results:
                        if 'income' not in detail:
                            detail.update({
                                'income': 0.0,
                                'cost': 0.0,
                                'profit': 0.0
                            })

            # 将当前业务类型的结果添加到总结果中
            all_results.extend(type_results)

        except Exception as e:
            logger.error(f"{logger_prefix} Error querying {current_type} details: {e}", exc_info=True)
            # 继续处理其他业务类型，不中断整个流程
            continue

    logger.info(f"{logger_prefix} Total records queried: {len(all_results)} across {len(target_types)} business types")
    return all_results

# 按日期范围查询Booking级别的业务详情数据（包含转运业务的rt/利润数据）
def get_booking_details_with_transhipment(begin_date: str, end_date: str, logger_prefix: str = "") -> List[Dict]:
    """
    按日期范围查询Booking级别的业务详情数据，包含转运业务的利润数据

    实现思路（已优化）：
    1. 根据TRANSHIPMENT_NEED环境变量决定查询范围，只执行一次数据库查询
    2. 预构建转运数据索引，提高查找效率（O(1)复杂度）
    3. 在内存中筛选基础日期范围的数据作为返回结果
    4. 使用向量化操作处理转运数据

    性能优化：
    - 消除双重数据库查询，减少50%查询时间
    - 预构建转运索引，将查找复杂度从O(n*m)降低到O(1)
    - 使用pandas向量化操作提高处理效率

    Args:
        begin_date: 开始日期
        end_date: 结束日期
        logger_prefix: 日志前缀

    Returns:
        List[Dict]: Booking级别业务详情数据列表（包含transhipment_profit字段）
    """
    if not begin_date or not end_date:
        logger.info(f"{logger_prefix} No date range provided.")
        return []

    logger.info(f"{logger_prefix} Querying booking details with transhipment for date range {begin_date} to {end_date}")

    try:
        # 第一步：根据TRANSHIPMENT_NEED环境变量决定查询范围（优化：只执行一次查询）
        transhipment_need = os.getenv('TRANSHIPMENT_NEED', 'False').lower() == 'true'

        if transhipment_need:
            # 如果需要转运数据，扩展前后各30天
            from datetime import datetime, timedelta
            begin_dt = datetime.strptime(begin_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

            extended_begin = (begin_dt - timedelta(days=30)).strftime('%Y-%m-%d')
            extended_end = (end_dt + timedelta(days=30)).strftime('%Y-%m-%d')

            logger.info(f"{logger_prefix} TRANSHIPMENT_NEED=True, querying extended range: {extended_begin} to {extended_end}")
        else:
            # 如果不需要转运数据，使用原始日期范围
            extended_begin = begin_date
            extended_end = end_date
            logger.info(f"{logger_prefix} TRANSHIPMENT_NEED=False, using original range: {extended_begin} to {extended_end}")

        # 第二步：执行一次扩展范围的数据库查询（优化：消除双重查询）
        logger.info(f"{logger_prefix} Step 1: Querying all booking details for extended period")
        all_booking_details = query_business_details_by_date(
            begin_date=extended_begin,
            end_date=extended_end,
            business_type='all',
            include_business_no=True,
            include_profit=True,
            logger_prefix=f"{logger_prefix}[ALL_DATA]"
        )

        if not all_booking_details:
            logger.info(f"{logger_prefix} No booking details found for the extended period")
            return []

        # 第三步：转换为DataFrame并筛选基础日期范围的数据（优化：在内存中筛选）
        import pandas as pd
        all_bookings_df = pd.DataFrame(all_booking_details)

        # 筛选基础日期范围的数据作为返回结果
        if transhipment_need:
            # 需要从扩展数据中筛选出基础范围的数据
            from datetime import datetime
            begin_dt = datetime.strptime(begin_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

            # 假设有job_date字段用于筛选
            if 'job_date' in all_bookings_df.columns:
                all_bookings_df['job_date_dt'] = pd.to_datetime(all_bookings_df['job_date'])
                base_mask = (all_bookings_df['job_date_dt'] >= begin_dt) & (all_bookings_df['job_date_dt'] <= end_dt)
                base_booking_details = all_bookings_df[base_mask].to_dict('records')
            else:
                # 如果没有job_date字段，使用所有数据
                base_booking_details = all_booking_details
        else:
            # 不需要转运数据时，所有数据就是基础数据
            base_booking_details = all_booking_details

        logger.info(f"{logger_prefix} Step 2: Found {len(base_booking_details)} base bookings, {len(all_booking_details)} total bookings")

        # 第四步：预构建转运数据索引（性能优化：O(1)查找复杂度）
        logger.info(f"{logger_prefix} Step 3: Building transhipment index for efficient lookup")
        transhipment_index = {}
        if 'transhipment_id' in all_bookings_df.columns:
            # 筛选有效的转运数据
            transhipment_data = all_bookings_df[
                all_bookings_df['transhipment_id'].notna() &
                (all_bookings_df['transhipment_id'] != '') &
                (all_bookings_df['transhipment_id'] != '0')
            ]

            if len(transhipment_data) > 0:
                # 按transhipment_id分组构建索引
                for transhipment_id, group in transhipment_data.groupby('transhipment_id'):
                    if transhipment_id and str(transhipment_id).strip():
                        transhipment_index[str(transhipment_id)] = group

                logger.info(f"{logger_prefix} Built transhipment index with {len(transhipment_index)} groups")
            else:
                logger.info(f"{logger_prefix} No valid transhipment data found")

        # 第五步：为每个基础booking添加转运利润数据（使用索引优化）
        logger.info(f"{logger_prefix} Step 4: Processing transhipment profits for {len(base_booking_details)} bookings")

        for booking in base_booking_details:
            transhipment_profit = 0.0
            is_transhipment = booking.get('is_transhipment', 0)
            transhipment_id = booking.get('transhipment_id')

            if is_transhipment and transhipment_id and str(transhipment_id).strip():
                transhipment_id_str = str(transhipment_id).strip()

                # 使用预构建的索引进行快速查找（O(1)复杂度）
                if transhipment_id_str in transhipment_index:
                    same_transhipment_group = transhipment_index[transhipment_id_str]

                    # 排除当前booking记录
                    other_transhipment = same_transhipment_group[
                        same_transhipment_group['jb_id'] != booking.get('jb_id')
                    ]

                    if len(other_transhipment) > 0:
                        # 汇总对应的另一段业务的利润
                        transhipment_profit = other_transhipment['profit'].fillna(0).sum() if 'profit' in other_transhipment.columns else 0.0

                        logger.debug(f"{logger_prefix} Found transhipment data for booking {booking.get('jb_id')}: profit={transhipment_profit}")

            # 添加转运字段到booking数据中
            booking.update({
                'transhipment_profit': float(transhipment_profit)
            })

        logger.info(f"{logger_prefix} Successfully processed transhipment data for {len(base_booking_details)} bookings")
        return base_booking_details

    except Exception as e:
        logger.error(f"{logger_prefix} Error in get_booking_details_with_transhipment: {e}", exc_info=True)
        return []

# 按日期范围查询Job级别的业务详情数据（包含业务明细和利润数据）
def query_job_details_by_date(begin_date: str, end_date: str, include_profit: bool = True, logger_prefix: str = "") -> List[Dict]:
    """
    按日期范围查询Job级别的业务详情数据（包含业务明细和利润数据）
    与query_business_details_by_date的区别是，本函数查询的是Job明细，而不仅仅是Booking明细

    Args:
        begin_date: 开始日期
        end_date: 结束日期
        include_profit: 是否包含利润数据（默认True）
        logger_prefix: 日志前缀

    Returns:
        List[Dict]: Job级别业务详情数据列表（包含income/cost/profit字段和business_type_name字段）
    """
    if not begin_date or not end_date:
        logger.info(f"{logger_prefix} No date range provided.")
        return []

    # 定义所有业务类型
    all_business_types = ['sea_export', 'sea_import', 'sea_triangle', 'air']

    logger.info(f"{logger_prefix} Querying all job-level business types: {all_business_types}")

    # 选择对应的Job级别SQL
    sql_map = {
        'sea_export': SQL_JOB_SEA_EXPORT_DETAILS,
        'sea_import': SQL_JOB_SEA_IMPORT_DETAILS,
        'sea_triangle': SQL_JOB_SEA_TRIANGLE_DETAILS,
        'air': SQL_JOB_AIR_DETAILS
    }

    # 业务类型名称映射
    business_type_names = {
        'sea_export': '海运出口',
        'sea_import': '海运进口',
        'sea_triangle': '海运三角贸易',
        'air': '空运'
    }

    all_results = []

    # 循环处理每个业务类型
    for current_type in all_business_types:
        try:
            sql = sql_map[current_type]
            type_results = []

            with get_pooled_pro2_connection() as conn:
                with conn.cursor() as cursor:
                    logger.info(f"{logger_prefix} Executing {current_type} job details query for date range {begin_date} to {end_date}")
                    cursor.execute(sql, (begin_date, end_date))
                    rows = cursor.fetchall()

                    # 获取Job级别的列名（最终简化结构）
                    column_names = [
                        'job_file_id', 'job_file_no', 'type_id', 'operator_id', 'operator_name',
                        'is_checked', 'is_op_finished', 'job_handling_agent_code', 'job_handling_agent_name',
                        'vessel', 'voyage', 'etd_date', 'eta_date', 'pol_code', 'pod_code',
                        'operator_dept_name', 'job_date'
                    ]

                    # 转换为字典列表
                    for row in rows:
                        row_dict = {}
                        for i, value in enumerate(row):
                            if i < len(column_names):
                                row_dict[column_names[i]] = value

                        # 转换数值字段为float类型（Job级别暂无需要转换的数值字段）
                        numeric_fields = []
                        for field in numeric_fields:
                            if field in row_dict and row_dict[field] is not None:
                                try:
                                    row_dict[field] = float(row_dict[field])
                                except (ValueError, TypeError):
                                    row_dict[field] = 0.0
                            elif field in row_dict:
                                row_dict[field] = 0.0

                        # 添加业务类型信息
                        row_dict['business_type'] = current_type
                        row_dict['business_type_name'] = business_type_names[current_type]
                        type_results.append(row_dict)

                    logger.info(f"{logger_prefix} Successfully queried {len(type_results)} {current_type} job detail records")

            # 如果需要包含利润数据，则查询并合并
            if include_profit and type_results:
                try:
                    # 使用统一的Job级别利润查询SQL
                    with get_pooled_pro2_connection() as conn:
                        with conn.cursor() as cursor:
                            logger.info(f"{logger_prefix} Querying {current_type} job profit data for date range {begin_date} to {end_date}")
                            cursor.execute(SQL_JOB_PROFIT_UNIFIED, (begin_date, end_date))
                            profit_rows = cursor.fetchall()

                            # 将利润数据转换为字典，以job_file_id为键
                            profit_dict = {}
                            for row in profit_rows:
                                job_file_id = str(row[0])  # job_file_id
                                profit_dict[job_file_id] = {
                                    'income': float(row[2]) if row[2] else 0.0,  # income
                                    'cost': abs(float(row[3])) if row[3] else 0.0,  # cost (取绝对值)
                                    'profit': float(row[4]) if row[4] else 0.0  # job_profit
                                }

                            # 合并利润数据到详情数据中
                            for detail in type_results:
                                job_file_id = str(detail.get('job_file_id', ''))
                                if job_file_id in profit_dict:
                                    detail.update(profit_dict[job_file_id])
                                else:
                                    detail.update({
                                        'income': 0.0,
                                        'cost': 0.0,
                                        'profit': 0.0
                                    })

                            logger.info(f"{logger_prefix} Successfully merged profit data for {len(type_results)} {current_type} job records")

                except Exception as e:
                    logger.warning(f"{logger_prefix} Failed to query {current_type} job profit data, using zero values: {e}")
                    # 如果利润数据查询失败，设置为0值
                    for detail in type_results:
                        if 'income' not in detail:
                            detail.update({
                                'income': 0.0,
                                'cost': 0.0,
                                'profit': 0.0
                            })

            # 将当前业务类型的结果添加到总结果中
            all_results.extend(type_results)

        except Exception as e:
            logger.error(f"{logger_prefix} Error querying {current_type} job details: {e}", exc_info=True)
            # 继续处理其他业务类型，不中断整个流程
            continue

    logger.info(f"{logger_prefix} Total job records queried: {len(all_results)} across {len(all_business_types)} business types")
    return all_results

# 计算海运出口Job的集拼状态和容器数量
def get_sea_export_consol_status_by_job(job_file_id: int, logger_prefix: str = "") -> dict:
    """
    计算指定海运出口Job的集拼状态和容器数量

    判断逻辑：
    - service_mode = 1 or 3，并且 cargo_type = 1 的booking为集拼货物
    - 如果集拼货物booking数量 >= 3，并且 > 总booking数量的75%，则判定为集拼
    - 统计集拼容器的20'和40'数量

    Args:
        job_file_id: Job文件ID
        logger_prefix: 日志前缀

    Returns:
        dict: {
            'is_consol': 1表示集拼，0表示非集拼,
            'consol_20_count': 集拼20'容器数量,
            'consol_40_count': 集拼40'容器数量
        }
    """
    # 首先检查booking级别的集拼条件
    booking_sql = '''
        SELECT
            COUNT(*) as total_bookings,
            COUNT(CASE WHEN service_mode IN (1,3) AND cargo_type = 1 THEN 1 END) as consol_bookings
        FROM
            SEA_EXPORT_BOOKING
        WHERE
            job_file_id = ?
            AND is_valid = 1
    '''

    # 查询集拼容器数量
    container_sql = '''
        SELECT
            COUNT(CASE WHEN cs.name LIKE '%20%' THEN 1 END) as consol_20_count,
            COUNT(CASE WHEN cs.name LIKE '%40%' OR cs.name LIKE '%45%' THEN 1 END) as consol_40_count
        FROM
            SEA_EXPORT_CONTAINER sec
        LEFT JOIN
            CONTAINER_SIZE cs ON sec.size_id = cs.id
        WHERE
            sec.job_file_id = ?
            AND sec.is_coload = 0
    '''

    try:
        with get_pooled_pro2_connection() as conn:
            with conn.cursor() as cursor:
                # 执行booking级别查询
                cursor.execute(booking_sql, (job_file_id,))
                booking_result = cursor.fetchone()

                # 初始化返回值
                result = {
                    'is_consol': 0,
                    'consol_20_count': 0,
                    'consol_40_count': 0
                }

                if booking_result:
                    total_bookings = booking_result[0] or 0
                    consol_bookings = booking_result[1] or 0

                    # 判断集拼条件
                    if consol_bookings >= 3 and total_bookings > 0:
                        consol_ratio = consol_bookings / total_bookings
                        is_consol = 1 if consol_ratio > 0.75 else 0

                        if is_consol == 1:
                            # 如果是集拼，查询容器数量
                            cursor.execute(container_sql, (job_file_id,))
                            container_result = cursor.fetchone()

                            if container_result:
                                result['consol_20_count'] = container_result[0] or 0
                                result['consol_40_count'] = container_result[1] or 0

                        result['is_consol'] = is_consol
                        logger.debug(f"{logger_prefix} Job {job_file_id} 集拼判断: 总booking={total_bookings}, 集拼booking={consol_bookings}, 比例={consol_ratio:.2%}, is_consol={is_consol}, 20'={result['consol_20_count']}, 40'={result['consol_40_count']}")
                    else:
                        logger.debug(f"{logger_prefix} Job {job_file_id} 集拼判断: 总booking={total_bookings}, 集拼booking={consol_bookings}, 不满足集拼条件, is_consol=0")

                return result

    except Exception as e:
        logger.error(f"{logger_prefix} Error calculating consol status for job {job_file_id}: {e}", exc_info=True)
        return {
            'is_consol': 0,
            'consol_20_count': 0,
            'consol_40_count': 0
        }

# 计算海运进口Job的集拼状态和容器数量
def get_sea_import_consol_status_by_job(job_file_id: int, job_bookings, logger_prefix: str = "") -> dict:
    """
    计算指定海运进口Job的集拼状态和容器数量

    判断逻辑：
    1. service_mode = 1 or 3 (从booking级别数据获取)
    2. 每个container内包含的BL数量 >= 3，并且 is_coload = 0
    3. 统计集拼容器的20'和40'数量

    Args:
        job_file_id: Job文件ID
        job_bookings: Job的booking数据 (DataFrame)
        logger_prefix: 日志前缀

    Returns:
        dict: {
            'is_consol': 1表示集拼，0表示非集拼,
            'consol_20_count': 集拼20'容器数量,
            'consol_40_count': 集拼40'容器数量
        }
    """

    # 初始化返回值
    result = {
        'is_consol': 0,
        'consol_20_count': 0,
        'consol_40_count': 0
    }

    # 首先检查service_mode条件
    if 'service_mode' in job_bookings.columns:
        service_mode_bookings = job_bookings[job_bookings['service_mode'].isin([1, 3])]
        if len(service_mode_bookings) == 0:
            logger.debug(f"{logger_prefix} Job {job_file_id} 集拼判断: service_mode不满足条件(1或3), is_consol=0")
            return result
    else:
        logger.debug(f"{logger_prefix} Job {job_file_id} 集拼判断: 缺少service_mode字段, is_consol=0")
        return result

    # 检查container级别的集拼条件和容器尺寸
    sql = '''
        SELECT
            sic.line_no,
            sic.is_coload,
            COUNT(DISTINCT sibc.bl_id) as bl_count_per_container,
            cs.name as container_size
        FROM
            sea_import_container sic
        LEFT JOIN
            sea_import_bl_container sibc ON sic.job_file_id = sibc.job_file_id AND sic.line_no = sibc.line_no
        LEFT JOIN
            container_size cs ON sic.size_id = cs.id
        WHERE
            sic.job_file_id = ?
        GROUP BY
            sic.line_no, sic.is_coload, cs.name
    '''

    try:
        with get_pooled_pro2_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, (job_file_id,))
                results = cursor.fetchall()

                if not results:
                    logger.debug(f"{logger_prefix} Job {job_file_id} 集拼判断: 没有找到container数据, is_consol=0")
                    return result

                # 检查是否有满足集拼条件的container
                consol_containers = 0
                total_containers = len(results)

                for row in results:
                    line_no, is_coload, bl_count_per_container, container_size = row

                    # 集拼条件：is_coload = 0 并且该container内BL数量 >= 3
                    if is_coload == 0 and (bl_count_per_container or 0) >= 3:
                        consol_containers += 1

                        # 统计容器尺寸
                        if container_size and '20' in container_size:
                            result['consol_20_count'] += 1
                        elif container_size and ('40' in container_size or '45' in container_size):
                            result['consol_40_count'] += 1

                        logger.debug(f"{logger_prefix} Job {job_file_id} Container {line_no}: is_coload={is_coload}, bl_count={bl_count_per_container}, size={container_size}, 满足集拼条件")
                    else:
                        logger.debug(f"{logger_prefix} Job {job_file_id} Container {line_no}: is_coload={is_coload}, bl_count={bl_count_per_container}, size={container_size}, 不满足集拼条件")

                # 如果有任何一个container满足集拼条件，则认为该Job为集拼
                result['is_consol'] = 1 if consol_containers > 0 else 0

                logger.debug(f"{logger_prefix} Job {job_file_id} 集拼判断: 总containers={total_containers}, 集拼containers={consol_containers}, is_consol={result['is_consol']}, 20'={result['consol_20_count']}, 40'={result['consol_40_count']}")
                return result

    except Exception as e:
        logger.error(f"{logger_prefix} Error calculating consol status for job {job_file_id}: {e}", exc_info=True)
        return result

# 计算海运出口Job的BL数量
def get_sea_export_bl_count_by_job(job_file_id: int, logger_prefix: str = "") -> int:
    """
    计算指定海运出口Job的BL数量

    Args:
        job_file_id: Job文件ID
        logger_prefix: 日志前缀

    Returns:
        int: BL数量
    """
    sql = '''
        SELECT
            COUNT(DISTINCT sebb.bl_id) AS bl_count
        FROM
            SEA_EXPORT_BOOKING seb
        LEFT JOIN
            JOB_FILE jf ON seb.JOB_FILE_ID = jf.ID
        LEFT JOIN
            SEA_EXPORT_BK_BL sebb ON seb.ID = sebb.BK_ID
        LEFT JOIN
            SEA_EXPORT_BL sebl ON sebb.BL_ID = sebl.ID
        WHERE
            seb.IS_VALID = 1
            AND (sebl.IS_VALID = 1 OR sebl.IS_VALID IS NULL)
            AND jf.IS_ACTIVE = 1
            AND seb.JOB_FILE_ID = ?
    '''

    try:
        with get_pooled_pro2_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, (job_file_id,))
                result = cursor.fetchone()
                bl_count = result[0] if result and result[0] is not None else 0
                logger.debug(f"{logger_prefix} Job {job_file_id} bl_count: {bl_count}")
                return bl_count
    except Exception as e:
        logger.error(f"{logger_prefix} Error calculating bl_count for job {job_file_id}: {e}", exc_info=True)
        return 0

# 从Booking表获取Job的指定货数据（用于86021特例处理）
def get_job_nominated_data_from_booking(begin_date: str, end_date: str, pro2_system_id: int = None, logger_prefix: str = "") -> Dict[str, Dict]:
    """
    从Booking表获取Job的指定货数据，专门用于86021的特例处理
    对于pro2_system_id=86021，需要应用特例处理来正确判断is_free_hand字段
    
    Args:
        begin_date: 开始日期
        end_date: 结束日期
        pro2_system_id: 分公司代码，86021需要特殊处理
        logger_prefix: 日志前缀
        
    Returns:
        Dict[str, Dict]: Job编号到指定货数据的映射
        格式: {
            'job_no': {
                'nominated_count': int,
                'nominated_rt': float,
                'nominated_teu': float
            }
        }
    """
    if not begin_date or not end_date:
        logger.info(f"{logger_prefix} No date range provided for nominated data query")
        return {}

    logger.info(f"{logger_prefix} Querying nominated data from booking for pro2_system_id={pro2_system_id}, date range: {begin_date} to {end_date}")
    
    try:
        # 获取该时间段内的所有booking数据
        booking_details = query_business_details_by_date(
            begin_date=begin_date,
            end_date=end_date,
            business_type='all',
            include_business_no=True,
            include_profit=True,
            logger_prefix=f"{logger_prefix}[NOMINATED]"
        )
        
        if not booking_details:
            logger.info(f"{logger_prefix} No booking details found for nominated data calculation")
            return {}
        
        # 转换为DataFrame以便处理
        import pandas as pd
        booking_df = pd.DataFrame(booking_details)
        
        job_nominated_map = {}
        
        # 按job_file_no分组处理
        if 'job_file_no' in booking_df.columns:
            job_groups = booking_df.groupby('job_file_no')
            
            for job_no, job_bookings in job_groups:
                job_data = []
                
                # 对每个booking应用86021特例处理逻辑
                for _, booking in job_bookings.iterrows():
                    booking_dict = booking.to_dict()
                    
                    # 应用86021特例处理
                    if pro2_system_id == 86021:
                        original_is_freehand = booking_dict.get('is_free_hand', 0)
                        salesman_dept_name = booking_dict.get('salesman_dept_name', '') or ''
                        
                        # 处理 is_free_hand 字段
                        if original_is_freehand == 0:
                            processed_is_freehand = 0  # 原本就是指定货
                        elif original_is_freehand == 1:
                            if salesman_dept_name == '指定货业务':
                                processed_is_freehand = 0  # 转换为指定货
                            else:
                                processed_is_freehand = 1  # 保持自揽货
                        else:
                            processed_is_freehand = 0  # 默认为指定货
                        
                        # 只有is_free_hand=0的才是指定货
                        if processed_is_freehand == 0:
                            job_data.append(booking_dict)
                    else:
                        # 其他分公司直接使用原始is_free_hand字段判断
                        if booking_dict.get('is_free_hand', 1) == 0:
                            job_data.append(booking_dict)
                
                # 计算该Job的指定货统计数据
                if job_data:
                    nominated_count = len(job_data)
                    nominated_rt = sum(float(record.get('lcl_rt', 0) or 0) for record in job_data)
                    nominated_teu = sum(float(record.get('teu', 0) or 0) for record in job_data)
                    
                    job_nominated_map[str(job_no)] = {
                        'nominated_count': nominated_count,
                        'nominated_rt': nominated_rt,
                        'nominated_teu': nominated_teu
                    }
                    
                    logger.debug(f"{logger_prefix} Job {job_no}: nominated_count={nominated_count}, nominated_rt={nominated_rt}")
        
        logger.info(f"{logger_prefix} Successfully calculated nominated data for {len(job_nominated_map)} jobs")
        return job_nominated_map
        
    except Exception as e:
        logger.error(f"{logger_prefix} Error in get_job_nominated_data_from_booking: {e}", exc_info=True)
        return {}

# 按日期范围查询Job级别的业务详情数据（包含完整统计数据）
def query_job_details_with_statistics_by_date(begin_date: str, end_date: str, logger_prefix: str = "") -> List[Dict]:
    """
    按日期范围查询Job级别的业务详情数据，包含完整的统计数据

    实现思路：
    1. 提取指定周期的所有job数据明细
    2. 根据TRANSHIPMENT_NEED环境变量决定booking数据查询范围：
       - 如果TRANSHIPMENT_NEED=True：扩展前后各1个月查询booking数据（为了转运业务数据完整性）
       - 如果TRANSHIPMENT_NEED=False或未设置：使用原始日期范围查询booking数据
    3. 对统计数据进行汇总，重点处理转运业务的查询和指定货业务的判断

    Args:
        begin_date: 开始日期
        end_date: 结束日期
        logger_prefix: 日志前缀

    Returns:
        List[Dict]: Job级别业务详情数据列表，包含完整统计数据
    """
    if not begin_date or not end_date:
        logger.info(f"{logger_prefix} No date range provided.")
        return []

    logger.info(f"{logger_prefix} Querying job details with statistics for date range {begin_date} to {end_date}")

    try:
        # 检查TRANSHIPMENT_NEED环境变量，决定是否扩展查询范围

        transhipment_need = os.getenv('TRANSHIPMENT_NEED', 'False').lower() == 'true'

        if transhipment_need:
            # 如果需要转运数据，扩展前后各1个月
            begin_dt = datetime.strptime(begin_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

            extended_begin = (begin_dt - timedelta(days=30)).strftime('%Y-%m-%d')
            extended_end = (end_dt + timedelta(days=30)).strftime('%Y-%m-%d')

            logger.info(f"{logger_prefix} TRANSHIPMENT_NEED=True, extended date range for booking data: {extended_begin} to {extended_end}")
        else:
            # 如果不需要转运数据，不扩展查询范围
            extended_begin = begin_date
            extended_end = end_date

            logger.info(f"{logger_prefix} TRANSHIPMENT_NEED=False, using original date range for booking data: {extended_begin} to {extended_end}")

        # 第一步：提取指定周期的Job数据明细
        logger.info(f"{logger_prefix} Step 1: Querying job details for core period")
        job_details = query_job_details_by_date(
            begin_date=begin_date,
            end_date=end_date,
            include_profit=True,
            logger_prefix=f"{logger_prefix}[JOB]"
        )

        if not job_details:
            logger.info(f"{logger_prefix} No job details found for the specified period")
            return []

        # 转换为DataFrame便于处理
        import pandas as pd
        job_df = pd.DataFrame(job_details)
        job_file_ids = job_df['job_file_id'].tolist()

        logger.info(f"{logger_prefix} Found {len(job_file_ids)} jobs to process statistics for")

        # 第二步：提取扩展周期的Booking级别数据
        logger.info(f"{logger_prefix} Step 2: Querying booking details for extended period")
        booking_details = query_business_details_by_date(
            begin_date=extended_begin,
            end_date=extended_end,
            business_type='all',
            include_business_no=True,
            include_profit=True,
            logger_prefix=f"{logger_prefix}[BOOKING]"
        )

        if not booking_details:
            logger.warning(f"{logger_prefix} No booking details found for extended period")
            # 返回Job数据，但统计字段为0
            for job in job_details:
                job.update({
                    'bk_count': 0,
                    'bl_count': 0,
                    'is_consol': 0,
                    'consol_20_count': 0,
                    'consol_40_count': 0,
                    'total_rt': 0.0,
                    'total_teu': 0.0,
                    'total_air_weight': 0.0,
                    'transhipment_count': 0,
                    'transhipment_rt': 0.0,
                    'transhipment_profit': 0.0,
                    # 所有指定货统计
                    'all_nominated_count': 0,
                    'all_nominated_rt': 0.0,
                    'all_nominated_profit': 0.0,
                    # 目的港代理指定货统计
                    'port_agent_nominated_count': 0,
                    'port_agent_nominated_rt': 0.0,
                    'port_agent_nominated_profit': 0.0
                })
            return job_details

        # 转换为DataFrame
        booking_df = pd.DataFrame(booking_details)

        # 保留所有booking数据用于转运利润计算，但筛选相关数据用于其他统计
        all_bookings = booking_df  # 用于转运利润计算

        # 优化：使用更高效的筛选方式
        job_file_ids_set = set(job_file_ids)  # 转换为set以提高查找效率
        relevant_bookings = booking_df[booking_df['job_file_id'].isin(job_file_ids_set)]  # 用于其他统计

        logger.info(f"{logger_prefix} Found {len(relevant_bookings)} relevant booking records for statistics")
        logger.info(f"{logger_prefix} Total {len(all_bookings)} booking records available for transhipment profit calculation")

        # 第三步：优化的统计数据计算 - 使用groupby替代循环
        logger.info(f"{logger_prefix} Step 3: Calculating statistics by job_file_id (optimized)")

        job_statistics = {}

        # 预构建转运数据索引以提高查找效率
        logger.debug(f"{logger_prefix} Building transhipment index for faster lookup")
        transhipment_index = {}
        if 'transhipment_id' in all_bookings.columns:
            # 构建 transhipment_id -> booking records 的映射
            transhipment_data = all_bookings[all_bookings['transhipment_id'].notna() & (all_bookings['transhipment_id'] != '')]
            for transhipment_id, group in transhipment_data.groupby('transhipment_id'):
                if transhipment_id and str(transhipment_id).strip():
                    transhipment_index[transhipment_id] = group

        # 使用groupby一次性分组，避免重复的DataFrame筛选
        if len(relevant_bookings) > 0:
            booking_groups = relevant_bookings.groupby('job_file_id')
        else:
            booking_groups = {}

        # 批量查询数据库数据以减少数据库连接次数
        logger.debug(f"{logger_prefix} Preparing batch database queries")
        sea_export_jobs = []
        sea_import_jobs = []

        # 预先确定需要查询的job类型
        for job_file_id in job_file_ids:
            if job_file_id in booking_groups.groups:
                job_bookings = booking_groups.get_group(job_file_id)
                business_types = job_bookings['business_type'].unique() if 'business_type' in job_bookings.columns else []
                if 'sea_export' in business_types:
                    sea_export_jobs.append(job_file_id)
                if 'sea_import' in business_types:
                    sea_import_jobs.append(job_file_id)

        # 批量查询海运出口BL数量和集拼状态（如果有的话）
        sea_export_bl_counts = {}
        sea_export_consol_status = {}
        if sea_export_jobs:
            logger.debug(f"{logger_prefix} Batch querying BL counts and consol status for {len(sea_export_jobs)} sea export jobs")
            # 批量查询BL数量
            for job_id in sea_export_jobs:
                sea_export_bl_counts[job_id] = get_sea_export_bl_count_by_job(job_id, f"{logger_prefix}[BL_COUNT]")
                sea_export_consol_status[job_id] = get_sea_export_consol_status_by_job(job_id, f"{logger_prefix}[CONSOL]")

        # 处理每个job的统计数据
        for job_file_id in job_file_ids:
            if job_file_id not in booking_groups.groups:
                # 没有相关booking数据
                job_statistics[job_file_id] = {
                    'bk_count': 0,
                    'bl_count': 0,
                    'is_consol': 0,
                    'consol_20_count': 0,
                    'consol_40_count': 0,
                    'total_rt': 0.0,
                    'total_teu': 0.0,
                    'total_air_weight': 0.0,
                    'transhipment_count': 0,
                    'transhipment_rt': 0.0,
                    'transhipment_profit': 0.0,
                    # 所有指定货统计
                    'all_nominated_count': 0,
                    'all_nominated_rt': 0.0,
                    'all_nominated_profit': 0.0,
                    # 目的港代理指定货统计
                    'port_agent_nominated_count': 0,
                    'port_agent_nominated_rt': 0.0,
                    'port_agent_nominated_profit': 0.0
                }
                continue

            # 获取当前job的booking数据（已经通过groupby预分组）
            job_bookings = booking_groups.get_group(job_file_id)

            # 基础统计 - 根据业务类型正确计算bk_count和bl_count
            # 检查当前Job的业务类型组成
            business_types = job_bookings['business_type'].unique() if 'business_type' in job_bookings.columns else []

            # 初始化计数
            bk_count = 0
            bl_count = 0

            # 根据业务类型分别计算
            if 'sea_export' in business_types:
                # 海运出口：bk_count = booking数量，bl_count 使用预查询的结果
                sea_export_bookings = job_bookings[job_bookings['business_type'] == 'sea_export'] if 'business_type' in job_bookings.columns else job_bookings
                bk_count = len(sea_export_bookings)
                bl_count = sea_export_bl_counts.get(job_file_id, 0)  # 使用预查询的结果

                # 如果还有其他业务类型，累加其bl_count（非海运出口业务的bl_count就是记录数）
                other_business_bookings = job_bookings[job_bookings['business_type'] != 'sea_export'] if 'business_type' in job_bookings.columns else pd.DataFrame()
                if len(other_business_bookings) > 0:
                    bl_count += len(other_business_bookings)
                    # bk_count对于非海运出口业务为0，所以不累加
            else:
                # 海运进口/三角贸易/空运：bk_count = 0，bl_count = 记录数
                bk_count = 0
                bl_count = len(job_bookings)

            # 集拼判断逻辑
            is_consol = 0
            consol_20_count = 0
            consol_40_count = 0

            if 'sea_export' in business_types:
                # 海运出口集拼判断 - 使用预查询的结果
                consol_result = sea_export_consol_status.get(job_file_id, {'is_consol': 0, 'consol_20_count': 0, 'consol_40_count': 0})
                is_consol = consol_result['is_consol']
                consol_20_count = consol_result['consol_20_count']
                consol_40_count = consol_result['consol_40_count']
            elif 'sea_import' in business_types:
                # 海运进口集拼判断
                consol_result = get_sea_import_consol_status_by_job(job_file_id, job_bookings, f"{logger_prefix}[CONSOL]")
                is_consol = consol_result['is_consol']
                consol_20_count = consol_result['consol_20_count']
                consol_40_count = consol_result['consol_40_count']
            else:
                # 三角贸易和空运不存在集拼概念
                is_consol = 0
                consol_20_count = 0
                consol_40_count = 0

            # RT/TEU/Air Weight统计
            total_rt = job_bookings['lcl_rt'].fillna(0).sum() if 'lcl_rt' in job_bookings.columns else 0.0
            total_teu = job_bookings['teu'].fillna(0).sum() if 'teu' in job_bookings.columns else 0.0
                
            total_air_weight = job_bookings['air_weight'].fillna(0).sum() if 'air_weight' in job_bookings.columns else 0.0

            # 转运业务统计（is_transhipment不为0）
            if 'is_transhipment' in job_bookings.columns:
                transhipment_bookings = job_bookings[job_bookings['is_transhipment'].fillna(0) != 0]
            else:
                transhipment_bookings = pd.DataFrame()
            
            # 指定货业务过滤逻辑（用于nomi_count等统计）
            # 需要对86021系统应用特殊业务规则
            current_location = os.getenv('LOCATION', 'SH').upper()
            location_to_system_id = {
                'QD': 86532,  # 青岛
                'SH': 86021,  # 上海
                'HK': 852,    # 香港
                'TY': 8103    # 东京
            }
            current_pro2_system_id = location_to_system_id.get(current_location, 86021)
            
            if 'is_free_hand' in job_bookings.columns:
                # 对86021系统应用特殊规则
                if current_pro2_system_id == 86021:
                    # 应用86021特例：salesman_dept_name='指定货业务'的记录需要从is_free_hand=1转换为is_free_hand=0
                    all_nominated_mask = pd.Series([False] * len(job_bookings), index=job_bookings.index)
                    
                    for idx, row in job_bookings.iterrows():
                        original_is_freehand = row.get('is_free_hand', 1)
                        salesman_dept_name = row.get('salesman_dept_name', '') or ''
                        
                        # 应用86021特例逻辑
                        if original_is_freehand == 0:
                            is_nominated = True  # 原本就是指定货
                        elif original_is_freehand == 1:
                            if salesman_dept_name == '指定货业务':
                                is_nominated = True  # 转换为指定货
                                logger.debug(f"{logger_prefix} Job {job_file_id}: 86021特例转换 - booking {idx} from 自揽货 to 指定货 (dept: {salesman_dept_name})")
                            else:
                                is_nominated = False  # 保持自揽货
                        else:
                            is_nominated = True  # 默认为指定货
                        
                        all_nominated_mask[idx] = is_nominated
                    
                    all_nominated_bookings = job_bookings[all_nominated_mask]
                    logger.debug(f"{logger_prefix} Job {job_file_id}: 86021特例处理后指定货数量: {len(all_nominated_bookings)} (原始数量: {len(job_bookings[job_bookings['is_free_hand'].fillna(1) == 0])})")
                else:
                    # 其他系统使用原始逻辑
                    all_nominated_bookings = job_bookings[job_bookings['is_free_hand'].fillna(1) == 0]
                    logger.debug(f"{logger_prefix} Job {job_file_id}: 系统{current_pro2_system_id}使用标准逻辑，指定货数量: {len(all_nominated_bookings)}")
            else:
                all_nominated_bookings = pd.DataFrame()
                logger.debug(f"{logger_prefix} Job {job_file_id}: 没有is_free_hand字段，指定货数量: 0")

            transhipment_count = len(transhipment_bookings)
            transhipment_rt = transhipment_bookings['lcl_rt'].fillna(0).sum() if len(transhipment_bookings) > 0 and 'lcl_rt' in transhipment_bookings.columns else 0.0

            # 转运利润计算：使用预构建的索引进行快速查找
            transhipment_profit = 0.0
            if len(transhipment_bookings) > 0 and 'transhipment_id' in transhipment_bookings.columns:
                # 获取当前Job的所有转运ID
                current_transhipment_ids = transhipment_bookings['transhipment_id'].dropna().unique()

                if len(current_transhipment_ids) > 0:
                    # 使用预构建的索引快速查找转运数据
                    for transhipment_id in current_transhipment_ids:
                        if transhipment_id and str(transhipment_id).strip() and transhipment_id in transhipment_index:
                            # 从索引中获取相同transhipment_id的所有booking记录
                            same_transhipment = transhipment_index[transhipment_id]
                            # 排除当前Job的记录
                            other_transhipment = same_transhipment[same_transhipment['job_file_id'] != job_file_id]

                            if len(other_transhipment) > 0:
                                # 汇总对应的另一段业务的利润
                                other_profit = other_transhipment['profit'].fillna(0).sum()
                                transhipment_profit += other_profit

            # 1. 所有指定货业务统计（使用前面已计算的all_nominated_bookings）
            all_nominated_count = len(all_nominated_bookings)
            all_nominated_rt = all_nominated_bookings['lcl_rt'].fillna(0).sum() if len(all_nominated_bookings) > 0 and 'lcl_rt' in all_nominated_bookings.columns else 0.0
            all_nominated_profit = all_nominated_bookings['profit'].fillna(0).sum() if len(all_nominated_bookings) > 0 else 0.0

            # 2. 目的港代理指定货业务统计（基于已处理的指定货数据 & nomi_agent = job_handling_agent）
            # 目的港代理指定货的判断条件：
            # 1. 必须是指定货 (使用与all_nominated_bookings相同的判断逻辑，包括特例处理)
            # 2. nomi_agent_name = job_handling_agent_name (指定代理与工作档代理相同)
            if (len(all_nominated_bookings) > 0 and
                'nomi_agent_name' in all_nominated_bookings.columns and
                'job_handling_agent_name' in all_nominated_bookings.columns):

                port_agent_nominated_bookings = all_nominated_bookings[
                    (all_nominated_bookings['nomi_agent_name'].fillna('') == all_nominated_bookings['job_handling_agent_name'].fillna('')) &
                    (all_nominated_bookings['nomi_agent_name'].fillna('') != '') &  # 确保代理名称不为空
                    (all_nominated_bookings['job_handling_agent_name'].fillna('') != '')
                ]
            else:
                port_agent_nominated_bookings = pd.DataFrame()

            port_agent_nominated_count = len(port_agent_nominated_bookings)
            port_agent_nominated_rt = port_agent_nominated_bookings['lcl_rt'].fillna(0).sum() if len(port_agent_nominated_bookings) > 0 and 'lcl_rt' in port_agent_nominated_bookings.columns else 0.0
            port_agent_nominated_profit = port_agent_nominated_bookings['profit'].fillna(0).sum() if len(port_agent_nominated_bookings) > 0 else 0.0

            # 添加调试日志以跟踪修复效果
            if logger.isEnabledFor(logging.DEBUG):
                business_types_str = ', '.join(business_types) if business_types else 'None'
                logger.debug(f"{logger_prefix} Job {job_file_id}: 业务类型={business_types_str}, bk_count={bk_count}, bl_count={bl_count}")
                logger.debug(f"{logger_prefix} Job {job_file_id}: 指定货统计 - all_nominated={all_nominated_count}, port_agent_nominated={port_agent_nominated_count}")
                if current_pro2_system_id == 86021:
                    original_nominated_count = len(job_bookings[job_bookings['is_free_hand'].fillna(1) == 0]) if 'is_free_hand' in job_bookings.columns else 0
                    logger.debug(f"{logger_prefix} Job {job_file_id}: 86021特例 - 原始is_free_hand=0: {original_nominated_count}, 处理后指定货: {all_nominated_count}")

            # 保存统计结果
            job_statistics[job_file_id] = {
                'bk_count': bk_count,
                'bl_count': bl_count,
                'is_consol': is_consol,
                'consol_20_count': consol_20_count,
                'consol_40_count': consol_40_count,
                'total_rt': float(total_rt),
                'total_teu': float(total_teu),
                'total_air_weight': float(total_air_weight),
                'transhipment_count': transhipment_count,
                'transhipment_rt': float(transhipment_rt),
                'transhipment_profit': float(transhipment_profit),
                # 所有指定货统计
                'all_nominated_count': all_nominated_count,
                'all_nominated_rt': float(all_nominated_rt),
                'all_nominated_profit': float(all_nominated_profit),
                # 目的港代理指定货统计
                'port_agent_nominated_count': port_agent_nominated_count,
                'port_agent_nominated_rt': float(port_agent_nominated_rt),
                'port_agent_nominated_profit': float(port_agent_nominated_profit)
            }

        # 第四步：将统计数据合并到Job数据中
        logger.info(f"{logger_prefix} Step 4: Merging statistics into job details")

        for job in job_details:
            job_file_id = job['job_file_id']
            if job_file_id in job_statistics:
                job.update(job_statistics[job_file_id])
            else:
                # 如果没有统计数据，设置为0
                job.update({
                    'bk_count': 0,
                    'bl_count': 0,
                    'is_consol': 0,
                    'consol_20_count': 0,
                    'consol_40_count': 0,
                    'total_rt': 0.0,
                    'total_teu': 0.0,
                    'total_air_weight': 0.0,
                    'transhipment_count': 0,
                    'transhipment_rt': 0.0,
                    'transhipment_profit': 0.0,
                    # 所有指定货统计
                    'all_nominated_count': 0,
                    'all_nominated_rt': 0.0,
                    'all_nominated_profit': 0.0,
                    # 目的港代理指定货统计
                    'port_agent_nominated_count': 0,
                    'port_agent_nominated_rt': 0.0,
                    'port_agent_nominated_profit': 0.0
                })

        logger.info(f"{logger_prefix} Successfully calculated statistics for {len(job_details)} jobs")
        return job_details

    except Exception as e:
        logger.error(f"{logger_prefix} Error in query_job_details_with_statistics_by_date: {e}", exc_info=True)
        return []



# 通过公司名称（部分匹配）获得公司列表
def get_company_list(part_name: str) -> List[Dict[str, Any]]:
    """
    通过公司名称（部分匹配）获得公司列表 - 大小写不敏感
    """
    sql = """
        SELECT code, name, address FROM company
        WHERE UPPER(name) LIKE UPPER(?) AND is_active = 1
        ORDER BY name
    """
    search_pattern = f'%{part_name}%'

    # 改进编码策略：UTF-8优先，然后尝试其他中文编码
    encodings_to_try = ['UTF-8', 'UTF-8', 'GB2312', 'BIG5', 'WIN1252', 'ISO8859_1']

    for encoding in encodings_to_try:
        try:
            results = execute_pro2_query(sql, (search_pattern,), fetch_all=True, charset=encoding)
            logger.info(f"成功使用 {encoding} 编码查询公司列表")
            break
        except UnicodeDecodeError as e:
            logger.warning(f"{encoding} 编码查询失败，尝试下一个编码: {e}")
            continue
        except Exception as e:
            logger.warning(f"{encoding} 编码查询失败: {e}")
            continue
    else:
        # 所有编码都失败，尝试无编码指定
        logger.warning("所有指定编码都失败，使用默认连接")
        try:
            results = execute_pro2_query(sql, (search_pattern,), fetch_all=True)
        except Exception as e:
            logger.error(f"所有编码尝试都失败: {e}")
            return []

    result_json = []
    for result in results:
        try:
            result_json.append({
                'code': str(result[0]) if result[0] else '',
                'name': str(result[1]) if result[1] else ''
            })
        except (UnicodeDecodeError, TypeError) as e:
            logger.warning(f"解析查询结果时编码错误: {e}, 跳过此条记录")
            continue

    return result_json

# 通过full_name/username获得user info
def get_user_info(part_name: str) -> List[Dict[str, Any]]:
    """
    通过full_name/username获得user info - 大小写不敏感
    """
    # 1. 修改SQL语句，使用UPPER函数进行大小写不敏感匹配
    sql = """
        SELECT user_id, username, full_name, ud.name as dept_name
        FROM users
        LEFT JOIN users_department ud ON users.dept_id = ud.dept_id
        WHERE (UPPER(full_name) LIKE UPPER(?) OR UPPER(username) LIKE UPPER(?))
        AND is_active = 1
        ORDER BY full_name, username
    """
    # 2. 在传递参数时，拼接好'%'
    search_pattern = f'%{part_name}%'

    # 改进编码策略：UTF-8优先，然后尝试其他中文编码
    encodings_to_try = ['UTF-8', 'UTF-8', 'GB2312', 'BIG5', 'WIN1252', 'ISO8859_1']

    for encoding in encodings_to_try:
        try:
            results = execute_pro2_query(sql, (search_pattern, search_pattern), fetch_all=True, charset=encoding)
            logger.info(f"成功使用 {encoding} 编码查询用户信息")
            break
        except UnicodeDecodeError as e:
            logger.warning(f"{encoding} 编码查询失败，尝试下一个编码: {e}")
            continue
        except Exception as e:
            logger.warning(f"{encoding} 编码查询失败: {e}")
            continue
    else:
        # 所有编码都失败，尝试无编码指定
        logger.warning("所有指定编码都失败，使用默认连接")
        try:
            results = execute_pro2_query(sql, (search_pattern, search_pattern), fetch_all=True)
        except Exception as e:
            logger.error(f"所有编码尝试都失败: {e}")
            return []

    result_json = []
    for row in results:
        try:
            result_json.append({
                'user_id': int(row[0]) if row[0] else 0,
                'username': str(row[1]) if row[1] else '',
                'full_name': str(row[2]) if row[2] else '',
                'dept_name': str(row[3]) if row[3] else ''
            })
        except (UnicodeDecodeError, TypeError, ValueError) as e:
            logger.warning(f"解析用户查询结果时编码错误: {e}, 跳过此条记录")
            continue

    return result_json
