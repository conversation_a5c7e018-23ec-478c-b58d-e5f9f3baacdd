# 业务数据的分析, 包括对特例情况的修正
"""
- 通过对MySQL数据库表t_job_details和表t_booking_details的分析, 获取到业务分析数据 --> 特例: 对于pro2_system_id的值为86021的业务, Job表中的如下字段数据, 需要通过对Booking表(特例修正后的)的汇总数据进行分析获得:
    1. 'all_nominated_count',      # 全部指定货票数 --> 特例修正后的is_free_hand字段为0的booking的'business_no'数量(count)
    2. 'all_nominated_rt',         # 全部指定货RT --> 特例修正后的is_free_hand字段为0的booking的'lcl_rt'合计值(sum)
- 参考脚本 utils/basic/profit_data_scheduler.py 中对于t_job_details和t_booking_details表的定义和中文名称含义映射
- Job & Booking 表的pro2_system_id的数值, 映射了不同给的分公司的业务数据: 86532-青岛(港口代码QDO), 86021-上海(港口代码SHA), 852-香港(港口代码HKG), 8103-东京(港口代码TKY)

对于Booking业务, 需要分析的基础数据包括: 
    'business_type_name',    # 业务类型
    'job_date',             # 工作档日期
    'job_file_no',          # 工作档编号
    'business_no',          # 订舱提单编号
    'shipper_name',         # 客户名称
    'vessel',               # 船名
    'voyage',               # 航次 
    'pol_code',             # 提单起运地 --> 如果提单起运地为空, 则使用sailing_pol的值
    'pod_code',             # 提单卸货地 --> 如果提单卸货地为空, 则使用pro2_system_id的值对应的港口代码(特例:pro2_system_id的值为86532除外, 因为86532的卸货港需要通过'操作部门'判断)
    'service_mode',         # 服务模式
    'lcl_rt',               # 拼箱RT
    'teu',                  # TEU
    'air_weight',           # 空运重量
    'income',               # 收入
    'cost',                 # 成本
    'profit',               # 利润
    'is_transhipment',      # 是否转运
    'transhipment_profit',  # 转运利润
    'is_free_hand',         # 自揽货 --> 特例: 对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断
    'salesman_name',        # 业务员 --> 特例: 对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断
    'salesman_dept_name',   # 营业员部门 --> 特例: 对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断
    'nomi_agent_name',      # 指定货代理 --> 特例: 对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断
    'operator_name',        # 操作员
    'operator_dept_name',   # 操作部门
    'coloader_name',        # Coloader名称
    'job_handling_agent_name'  # 工作档代理

对于Job业务, 需要分析的基础数据包括: 
    'business_type_name',  # 业务类型
    'job_date',           # 工作档日期
    'job_file_no',        # 工作档编号
    'vessel',             # 船名
    'voyage',             # 航次
    'pol_code',           # 起运港
    'pod_code',           # 卸货港 --> 如果卸货港为空, 则使用pro2_system_id的值对应的港口代码(特例:pro2_system_id的值为86532除外, 因为86532的卸货港需要通过'操作部门'判断)
    'bk_count',           # 订舱数
    'bl_count',           # 提单数
    'total_rt',           # 计费吨
    'total_teu',          # TEU
    'income',             # 收入
    'cost',               # 成本
    'profit',             # 利润
    'transhipment_count', # 转运票数
    'transhipment_profit', # 转运利润
    'operator_name',      # 操作员
    'job_handling_agent_name',  # 工作档代理
    'all_nominated_count',      # 全部指定货票数
    'all_nominated_rt',         # 全部指定货RT
    'port_agent_nominated_count', # 港代指定货票数
    'port_agent_nominated_rt',    # 港代指定货RT
    'is_consol',               # 是否集拼
    'consol_20_count',        # 20集拼量
    'consol_40_count',        # 40集拼量
    'operator_dept_name',     # 操作部门
    'is_op_finished',         # 操作完成
    'is_checked'              # 审核状态

** 对于特例情况的值的判断规则: **
对于pro2_system_id的值为86532的业务, pod_code(卸货港)的值为空时, 需要通过'操作部门'判断对应的卸货港的规则:
    1. 如果'操作部门'以'QD/'开头, 则pod_code的值视为'QDO'
    2. 如果'操作部门'以'GZ/'开头, 则pod_code的值视为'GZG'
    3. 如果'操作部门'以'NB/'开头, 则pod_code的值视为'NBO'
    4. 如果'操作部门'以'XM/'开头, 则pod_code的值视为'XMN'

对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断'is_free_hand'字段的视为值:
    1. 如果'is_free_hand'字段的值为0, 则'is_free_hand'字段的值视为0
    2. 如果'is_free_hand'字段的值为1, 则:
        2.1 如果'salesman_dept_name'字段的值为'指定货业务', 则'is_free_hand'字段的值视为0
        2.2 如果'salesman_dept_name'字段的值不为'指定货业务', 则'is_free_hand'字段的值视为1

对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断'salesman_name'字段的视为值:
    1. 如果'is_free_hand'字段的值为0, 则'salesman_name'字段的值视为不变
    2. 如果'is_free_hand'字段的值为1, 则:
        2.1 如果'salesman_dept_name'字段的值为'指定货业务', 则'salesman_name'字段的值视为空值
        2.2 如果'salesman_dept_name'字段的值不为'指定货业务', 则'salesman_name'字段的值视为不变

对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断'salesman_dept_name'字段的视为值:
    1. 如果'is_free_hand'字段的值为0, 则'salesman_dept_name'字段的值视为不变
    2. 如果'is_free_hand'字段的值为1, 则:
        2.1 如果'salesman_dept_name'字段的值为'指定货业务', 则'salesman_dept_name'字段的值视为空值
        2.2 如果'salesman_dept_name'字段的值不为'指定货业务', 则'salesman_dept_name'字段的值视为不变

对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断'nomi_agent_name'字段的视为值:
    1. 如果'is_free_hand'字段的值为0, 则'nomi_agent_name'字段的值视为不变
    2. 如果'is_free_hand'字段的值为1, 则:
        2.1 如果'salesman_dept_name'字段的值为'指定货业务', 则'nomi_agent_name'字段的视为值:
            2.1.1 如果'salesman_name'字段的值为包含'指定货'或者'指定'字样, 则'nomi_agent_name'字段的值视为'salesman_name'字段的值删除'指定货'或者'指定'字样后的值
            2.1.2 如果'salesman_name'字段的值为不包含'指定货'或者'指定'字样, 则'nomi_agent_name'字段的值视为'salesman_name'字段的值
        2.2 如果'salesman_dept_name'字段的值不为'指定货业务', 则'nomi_agent_name'字段的值视为空值

"""
# 对常用的分析场景, 使用固定函数进行分析
# 对于常用场景之外的分析场景, 使用动态函数进行分析: 给出示例sql, 由AI根据用户需求进行实际SQL的构建（暂时不需要实现）

import asyncio
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from typing import List, Dict, Any, Optional
import sys
import os
import aiomysql
import traceback
from collections import defaultdict
import pandas as pd

from utils.basic.data_conn_unified import get_mysql_connection, MYSQL_DB_MCP
from utils.basic.data_cache_manager import async_cached_data_function

# 生产环境优化的路径处理
def _setup_project_path():
    """
    设置项目根目录到Python路径中
    适用于开发环境和生产环境（Ubuntu服务器）
    """
    try:
        # 获取当前文件的绝对路径
        current_file = os.path.abspath(__file__)
        
        # 计算项目根目录（向上两级目录）
        project_root = os.path.dirname(os.path.dirname(current_file))
        
        # 验证项目根目录是否存在utils目录
        if os.path.exists(os.path.join(project_root, 'utils')):
            # 只有在路径不存在时才添加，避免重复
            if project_root not in sys.path:
                sys.path.insert(0, project_root)  # 使用insert(0)确保优先级
        else:
            # 如果找不到utils目录，使用环境变量或当前工作目录
            fallback_root = os.getenv('MCP_CMS_ROOT', os.getcwd())
            if fallback_root not in sys.path:
                sys.path.insert(0, fallback_root)
    except Exception as e:
        # 生产环境的错误处理
        print(f"Warning: Failed to setup project path: {e}")
        # 使用当前工作目录作为后备方案
        if os.getcwd() not in sys.path:
            sys.path.insert(0, os.getcwd())

# 执行路径设置
_setup_project_path()

#########################################################################
# 数据转换辅助函数
#########################################################################

def example_usage_unified_format():
    """
    新的统一返回格式使用示例 - 已优化为按columns_info顺序排列
    
    所有分析类函数(analysis_xxx)和提取类函数现在都返回统一格式:
    {
        "json_data": 原始JSON数据,
        "pd_data": pandas.DataFrame格式数据(按columns_info顺序排列),
        "metadata": {
            "data_type": "数据类型标识",
            "query_info": "查询信息",
            "columns_info": "列说明字典(决定DataFrame列顺序)"
        }
    }
    
    主要优化内容:
    1. DataFrame列顺序严格按照columns_info的key顺序排列
    2. 只包含columns_info中存在的字段
    3. get_sea_air_profit_from_tokens_table_cached函数新增nomi_agent_name字段
    4. 对86021分公司特例处理：指定货时清空salesman字段，使用nomi_agent_name
    
    使用示例:
    
    # 1. 调用分析函数
    result = await analysis_booking_by_month_data('2024-01-01', 3, 86021)
    
    # 2. 获取DataFrame数据进行分析(已按指定顺序排列)
    df = result['pd_data']
    print(df.head())
    print(df.columns.tolist())  # 查看列顺序
    print(df.groupby('business_type')['profit'].sum())
    
    # 3. 获取原始JSON数据
    json_data = result['json_data']
    
    # 4. 查看元数据信息
    metadata = result['metadata']
    print(f"数据类型: {metadata['data_type']}")
    print(f"列说明: {metadata['columns_info']}")
    
    # 5. 特殊功能：86021指定货处理示例
    booking_result = await get_sea_air_profit_from_tokens_table_cached('2024-01-01', '2024-01-31', 86021)
    booking_df = booking_result['pd_data']
    # 对于86021且is_freehand=0的记录，salesman_name/salesman_department为空，nomi_agent_name有值
    nominated_records = booking_df[booking_df['is_freehand'] == 0]
    print(nominated_records[['salesman_name', 'salesman_department', 'nomi_agent_name']].head())
    
    修改的函数列表:
    
    分析类函数:
    - analysis_booking_by_month_data (重命名自analysis_by_month_data)
    - analysis_job_by_month_data (重命名自analysis_by_month_data)
    - analysis_booking_by_customer  
    - analysis_booking_by_top_n_customers
    - analysis_job_consol_line
    - analysis_booking_nomi_agents
    - analysis_booking_top_n_rt
    
    提取类函数:
    - get_sea_air_profit_from_tokens_table_cached (新增nomi_agent_name字段处理)
    - get_job_details_from_tokens_table_cached
    """
    pass

def convert_monthly_analysis_to_dataframe(data: List[Dict[str, Any]], analysis_type: str, columns_info: Dict[str, str]) -> pd.DataFrame:
    """
    将月度分析数据转换为DataFrame格式，按照columns_info顺序排列
    
    Args:
        data: 月度分析数据列表
        analysis_type: 分析类型 (booking/job)
        columns_info: 列信息字典，用于确定列顺序和过滤字段
        
    Returns:
        pd.DataFrame: 转换后的DataFrame
    """
    if not data:
        return pd.DataFrame()
    
    rows = []
    for month_data in data:
        year = month_data.get('year')
        month = month_data.get('month')
        pro2_system_id = month_data.get('pro2_system_id')
        
        # 处理各业务类型数据
        business_types = [
            ('sea_export', month_data.get('sea_export_data', [])),
            ('sea_import', month_data.get('sea_import_data', [])),
            ('triangle_trade', month_data.get('triangle_trade_data', [])),
            ('air', month_data.get('air_data', []))
        ]
        
        for business_type, type_data in business_types:
            if type_data:
                for item in type_data:
                    row = {
                        'year': year,
                        'month': month,
                        'pro2_system_id': pro2_system_id,
                        'business_type': business_type,
                        'analysis_type': analysis_type
                    }
                    row.update(item)
                    rows.append(row)
    
    df = pd.DataFrame(rows)
    
    # 只保留columns_info中存在的字段，并按照columns_info的顺序排列
    if not df.empty and columns_info:
        available_columns = [col for col in columns_info.keys() if col in df.columns]
        df = df[available_columns]
    
    return df

def convert_customer_analysis_to_dataframe(data: List[Dict[str, Any]], columns_info: Dict[str, str]) -> pd.DataFrame:
    """
    将客户分析数据转换为DataFrame格式，按照columns_info顺序排列
    """
    if not data:
        return pd.DataFrame()
    
    rows = []
    for month_data in data:
        year = month_data.get('year')
        month = month_data.get('month')
        pro2_system_id = month_data.get('pro2_system_id')
        customer_name = month_data.get('customer_name')
        
        for pod_data in month_data.get('data', []):
            row = {
                'year': year,
                'month': month,
                'pro2_system_id': pro2_system_id,
                'customer_name': customer_name
            }
            row.update(pod_data)
            rows.append(row)
    
    df = pd.DataFrame(rows)
    
    # 只保留columns_info中存在的字段，并按照columns_info的顺序排列
    if not df.empty and columns_info:
        available_columns = [col for col in columns_info.keys() if col in df.columns]
        df = df[available_columns]
    
    return df

def convert_entity_analysis_to_dataframe(data: List[Dict[str, Any]], entity_type: str, columns_info: Dict[str, str]) -> pd.DataFrame:
    """
    将实体分析数据转换为DataFrame格式（适用于客户、代理、港口等），按照columns_info顺序排列
    
    Args:
        data: 实体分析数据列表
        entity_type: 实体类型 (customer/agent/port/consol_line)
        columns_info: 列信息字典，用于确定列顺序和过滤字段
    """
    if not data:
        return pd.DataFrame()
    
    rows = []
    for entity_data in data:
        # 获取实体标识字段
        entity_fields = {}
        if entity_type == 'customer':
            entity_fields['customer_name'] = entity_data.get('customer_name')
        elif entity_type == 'agent':
            entity_fields['nomi_agent_name'] = entity_data.get('nomi_agent_name')
        elif entity_type == 'port':
            entity_fields['pod_code'] = entity_data.get('pod_code')
        elif entity_type == 'consol_line':
            entity_fields['consol_line'] = entity_data.get('consol_line')
        
        pro2_system_id = entity_data.get('pro2_system_id')
        
        for month_data in entity_data.get('data', []):
            row = {
                'pro2_system_id': pro2_system_id,
                'entity_type': entity_type
            }
            row.update(entity_fields)
            row.update(month_data)
            rows.append(row)
    
    df = pd.DataFrame(rows)
    
    # 只保留columns_info中存在的字段，并按照columns_info的顺序排列
    if not df.empty and columns_info:
        available_columns = [col for col in columns_info.keys() if col in df.columns]
        df = df[available_columns]
    
    return df

def convert_records_to_dataframe(records: List[Dict[str, Any]], columns_info: Dict[str, str]) -> pd.DataFrame:
    """
    将记录列表转换为DataFrame格式，按照columns_info顺序排列
    """
    if not records:
        return pd.DataFrame()
    
    df = pd.DataFrame(records)
    
    # 只保留columns_info中存在的字段，并按照columns_info的顺序排列
    if not df.empty and columns_info:
        available_columns = [col for col in columns_info.keys() if col in df.columns]
        df = df[available_columns]
    
    return df

#########################################################################
# 分析工具函数
#########################################################################

# 分析工具函数: 应用基本数据清理（轻量级，用于数据提取阶段）
def apply_basic_data_cleaning(record: Dict[str, Any]) -> Dict[str, Any]:
    """
    应用基本数据清理逻辑（轻量级版本，用于数据提取阶段）
    只进行必要的字段映射和基本数据清理，不进行复杂的业务逻辑转换

    Args:
        record: 数据记录

    Returns:
        清理后的数据记录
    """
    processed_record = record.copy()
    pro2_system_id = record.get('pro2_system_id')

    # 通用处理：为所有系统设置is_freehand字段（兼容不同字段名）
    if 'is_freehand' not in processed_record:
        # 优先使用is_free_hand字段，因为这是SQL查询返回的字段名
        original_is_freehand = record.get('is_free_hand', record.get('is_freehand', 0))
        processed_record['is_freehand'] = original_is_freehand

    # 处理提单起运地特例：如果提单起运地为空，则使用工作档起运地的值
    if not processed_record.get('bill_pol') or processed_record.get('bill_pol') == '':
        processed_record['bill_pol'] = processed_record.get('job_pol', '')

    # 处理 pro2_system_id = 86532 的特例
    if pro2_system_id == 86532:
        # 处理 pod_code 为空的情况
        if not record.get('bill_pod') or record.get('bill_pod') == '':
            operator_dept = record.get('operator_department', '')
            if operator_dept.startswith('QD/'):
                processed_record['bill_pod'] = 'QDO'
            elif operator_dept.startswith('GZ/'):
                processed_record['bill_pod'] = 'GZG'
            elif operator_dept.startswith('NB/'):
                processed_record['bill_pod'] = 'NBO'
            elif operator_dept.startswith('XM/'):
                processed_record['bill_pod'] = 'XMN'

    # 为所有记录添加基本的 nomi_agent_name 字段（如果不存在）
    if 'nomi_agent_name' not in processed_record:
        processed_record['nomi_agent_name'] = record.get('job_handling_agent', '') or ''

    return processed_record

# 分析工具函数: 应用特例处理逻辑（完整版本，用于数据分析阶段）
def apply_special_case_processing(record: Dict[str, Any]) -> Dict[str, Any]:
    """
    应用特例处理逻辑
    
    Args:
        record: 数据记录
        
    Returns:
        处理后的数据记录
    """
    processed_record = record.copy()
    pro2_system_id = record.get('pro2_system_id')
    
    # 通用处理：为所有系统设置is_freehand字段（兼容不同字段名）
    if 'is_freehand' not in processed_record:
        # 优先使用is_free_hand字段，因为这是SQL查询返回的字段名
        original_is_freehand = record.get('is_free_hand', record.get('is_freehand', 0))
        processed_record['is_freehand'] = original_is_freehand
    
    # 处理提单起运地特例：如果提单起运地为空，则使用工作档起运地的值
    if not processed_record.get('bill_pol') or processed_record.get('bill_pol') == '':
        processed_record['bill_pol'] = processed_record.get('job_pol', '')
    
    # 处理 pro2_system_id = 86532 的特例
    if pro2_system_id == 86532:
        # 处理 pod_code 为空的情况
        if not record.get('bill_pod') or record.get('bill_pod') == '':
            operator_dept = record.get('operator_department', '')
            if operator_dept.startswith('QD/'):
                processed_record['bill_pod'] = 'QDO'
            elif operator_dept.startswith('GZ/'):
                processed_record['bill_pod'] = 'GZG'
            elif operator_dept.startswith('NB/'):
                processed_record['bill_pod'] = 'NBO'
            elif operator_dept.startswith('XM/'):
                processed_record['bill_pod'] = 'XMN'
    
    # 处理其他分公司的卸货港为空特例：使用pro2_system_id对应的港口代码
    if not processed_record.get('bill_pod') or processed_record.get('bill_pod') == '':
        if pro2_system_id == 86021:
            processed_record['bill_pod'] = 'SHA'
        elif pro2_system_id == 852:
            processed_record['bill_pod'] = 'HKG'
        elif pro2_system_id == 8103:
            processed_record['bill_pod'] = 'TKY'
    
    # 处理 pro2_system_id = 86021 的特例
    if pro2_system_id == 86021:
        # 兼容不同的字段名称 - SQL查询返回的字段名是is_free_hand
        original_is_freehand = record.get('is_free_hand', record.get('is_freehand', 0))
        # 兼容不同的字段名称
        salesman_dept_name = record.get('salesman_dept_name', '') or record.get('salesman_department', '')
        salesman_name = record.get('salesman_name', '')
        
        # 处理 is_freehand 字段
        if original_is_freehand == 0:
            processed_record['is_freehand'] = 0
        elif original_is_freehand == 1:
            if salesman_dept_name == '指定货业务':
                processed_record['is_freehand'] = 0
                # 对于从"指定货业务"部门转换来的指定货，清空salesman字段
                processed_record['salesman_name'] = None
                processed_record['salesman_department'] = None
                processed_record['salesman_dept_name'] = None
            else:
                processed_record['is_freehand'] = 1
        
        # 处理 nomi_agent_name 字段（为记录添加这个字段）
        # 对于指定货（is_freehand=0），需要正确设置nomi_agent_name
        if processed_record['is_freehand'] == 0:
            # 如果原始记录来自"指定货业务"部门，从salesman_name中提取代理名称
            if salesman_dept_name == '指定货业务':
                if salesman_name:
                    if '指定货' in salesman_name:
                        processed_record['nomi_agent_name'] = salesman_name.replace('指定货', '').strip()
                    elif '指定' in salesman_name:
                        processed_record['nomi_agent_name'] = salesman_name.replace('指定', '').strip()
                    else:
                        processed_record['nomi_agent_name'] = salesman_name.strip()
                else:
                    processed_record['nomi_agent_name'] = ''
            else:
                # 对于不是来自"指定货业务"部门的86021指定货，使用job_handling_agent
                processed_record['nomi_agent_name'] = record.get('job_handling_agent', '') or ''
        else:
            # 非指定货，清空nomi_agent_name
            processed_record['nomi_agent_name'] = ''
    
    # 为所有记录添加 nomi_agent_name 字段（如果不存在）
    if 'nomi_agent_name' not in processed_record:
        # 修正：使用job_handling_agent作为指定货代理字段，因为bl_handling_agent字段基本为空
        processed_record['nomi_agent_name'] = record.get('job_handling_agent', '')
    
    return processed_record

# 分析工具函数: 获取业务类型映射
def get_business_type_mapping() -> Dict[str, str]:
    """
    获取业务类型映射
    
    Returns:
        业务类型映射字典
    """
    return {
        '海运出口': 'sea_export',
        '海运进口': 'sea_import', 
        '海运三角贸易': 'triangle_trade',  # 修正：数据库中是"海运三角贸易"
        '空运': 'air'  # 修正：数据库中是"空运"而不是分出口和进口
    }

# 分析工具函数: 计算汇总数据
def calculate_aggregated_data(records: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    计算汇总数据
    
    Args:
        records: 数据记录列表
        
    Returns:
        汇总数据字典
    """
    if not records:
        return {
            "customer_count": 0,
            "bkbl_count": 0,
            "rt": 0,
            "teu": 0,
            "air_weight": 0,
            "income": 0,
            "cost": 0,
            "profit": 0,
            "profit_rate": 0,
            "all_nominated_count": 0,
            "all_nominated_rt": 0,
            "all_nominated_profit": 0,
            "nomi_agent_count": 0,
        }
    
    # 客户数量（去重）
    customers = set()
    for record in records:
        if record.get('client_name'):
            customers.add(record['client_name'])
    
    # 指定货代理数量（去重）
    nomi_agents = set()
    nominated_records = []
    for record in records:
        if record.get('is_freehand') == 0:  # 指定货
            nominated_records.append(record)
            # 修正：正确过滤空字符串和None值
            nomi_agent_name = record.get('nomi_agent_name')
            if nomi_agent_name and nomi_agent_name.strip():
                nomi_agents.add(nomi_agent_name.strip())
    
    # 基础汇总
    total_income = sum(float(record.get('income', 0) or 0) for record in records)
    total_cost = abs(sum(float(record.get('cost', 0) or 0) for record in records))
    total_profit = sum(float(record.get('profit', 0) or 0) for record in records)
    
    # 指定货汇总
    nominated_rt = sum(float(record.get('lcl_rt', 0) or 0) for record in nominated_records)
    nominated_profit = sum(float(record.get('profit', 0) or 0) for record in nominated_records)
    
    # 计算利润率
    profit_rate = (total_profit / total_income * 100) if total_income > 0 else 0
    
    return {
        "customer_count": len(customers),
        "bkbl_count": len(records),
        "rt": sum(float(record.get('lcl_rt', 0) or 0) for record in records),
        "teu": sum(float(record.get('teu', 0) or 0) for record in records),
        "air_weight": sum(float(record.get('air_weight', 0) or 0) for record in records),
        "income": total_income,
        "cost": total_cost,
        "profit": total_profit,
        "profit_rate": round(profit_rate, 2),
        "all_nominated_count": len(nominated_records),
        "all_nominated_rt": nominated_rt,
        "all_nominated_profit": nominated_profit,
        "nomi_agent_count": len(nomi_agents),
    }

# 分析工具函数: 计算Job数据汇总
def calculate_job_aggregated_data(records: List[Dict[str, Any]], business_type: str = '') -> Dict[str, Any]:
    """
    计算Job数据汇总
    
    Args:
        records: Job数据记录列表
        business_type: 业务类型，用于确定票数计算方式
        
    Returns:
        汇总数据字典
    """
    if not records:
        return {
            "bkbl_count": 0,
            "rt": 0,
            "teu": 0,
            "air_weight": 0,
            "income": 0,
            "cost": 0,
            "profit": 0,
            "profit_rate": 0,
            "all_nominated_count": 0,
            "all_nominated_rt": 0,
            "all_nominated_teu": 0,
            "consol_line_count": 0,
            "consol_rt": 0,
            "consol_20_count": 0,
            "consol_40_count": 0,
        }
    
    # 基础汇总
    total_income = sum(float(record.get('income', 0) or 0) for record in records)
    total_cost = abs(sum(float(record.get('cost', 0) or 0) for record in records))
    total_profit = sum(float(record.get('profit', 0) or 0) for record in records)
    
    # 票数计算：海运出口用bk_count，其他用bill_count
    if business_type == '海运出口':
        total_count = sum(int(record.get('bk_count', 0) or 0) for record in records)
    else:
        total_count = sum(int(record.get('bill_count', 0) or 0) for record in records)
    
    # 集拼航线数量计算：对于is_consolidation=1的记录，统计不同pol_code/pod_code组合的数量
    consol_lines = set()
    consol_records = []
    for record in records:
        if record.get('is_consolidation') == 1:
            consol_records.append(record)
            pol_code = record.get('pol_code', '')
            pod_code = record.get('pod_code', '')
            if pol_code and pod_code:
                consol_lines.add(f"{pol_code}/{pod_code}")
    
    # 集拼数据汇总
    consol_rt = sum(float(record.get('rt', 0) or 0) for record in consol_records)
    consol_20_count = sum(int(record.get('consolidation_20', 0) or 0) for record in consol_records)
    consol_40_count = sum(int(record.get('consolidation_40', 0) or 0) for record in consol_records)
    
    # 计算利润率
    profit_rate = (total_profit / total_income * 100) if total_income > 0 else 0
    
    return {
        "bkbl_count": total_count,
        "rt": sum(float(record.get('rt', 0) or 0) for record in records),
        "teu": sum(float(record.get('teu', 0) or 0) for record in records),
        "air_weight": 0,  # Job表中没有空运重量字段
        "income": total_income,
        "cost": total_cost,
        "profit": total_profit,
        "profit_rate": round(profit_rate, 2),
        "all_nominated_count": sum(int(record.get('nomi_count', 0) or 0) for record in records),
        "all_nominated_rt": sum(float(record.get('nomi_rt', 0) or 0) for record in records),
        "all_nominated_teu": 0,  # 需要从Booking表计算
        "consol_line_count": len(consol_lines),
        "consol_rt": consol_rt,
        "consol_20_count": consol_20_count,
        "consol_40_count": consol_40_count,
    }

# 分析工具函数: 应用Job数据的特例处理逻辑
def apply_special_case_processing_job(record: Dict[str, Any]) -> Dict[str, Any]:
    """
    应用Job数据的特例处理逻辑
    
    Args:
        record: Job数据记录
        
    Returns:
        处理后的数据记录
    """
    processed_record = record.copy()
    pro2_system_id = record.get('pro2_system_id')
    
    # 处理卸货港为空的特例
    if not record.get('pod_code') or record.get('pod_code') == '':
        # 86532特例: 根据操作部门判断
        if pro2_system_id == 86532:
            operator_dept = record.get('operator_department', '')
            if operator_dept.startswith('QD/'):
                processed_record['pod_code'] = 'QDO'
            elif operator_dept.startswith('GZ/'):
                processed_record['pod_code'] = 'GZG'
            elif operator_dept.startswith('NB/'):
                processed_record['pod_code'] = 'NBO'
            elif operator_dept.startswith('XM/'):
                processed_record['pod_code'] = 'XMN'
        else:
            # 其他分公司根据pro2_system_id映射港口代码
            if pro2_system_id == 86021:
                processed_record['pod_code'] = 'SHA'
            elif pro2_system_id == 852:
                processed_record['pod_code'] = 'HKG'
            elif pro2_system_id == 8103:
                processed_record['pod_code'] = 'TKY'
    
    return processed_record

# 分析工具函数: 从Booking表获取Job的指定货数据
async def get_job_nominated_data_from_booking(begin_date: str, end_date: str, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    从Booking表获取Job的指定货数据（用于86021特例）
    注意：对于86021，需要应用特例处理来正确判断is_freehand字段
    
    Args:
        begin_date: 开始日期
        end_date: 结束日期
        pro2_system_id: 分公司代码
        
    Returns:
        指定货数据汇总
    """
    # 如果是86021，需要获取更多字段来进行特例处理
    if pro2_system_id == 86021:
        # 使用窗口函数优化性能，直接在SQL中过滤指定货记录
        sql = """
            SELECT 
                job_no,
                is_freehand,
                salesman_department,
                lcl_rt,
                teu
            FROM (
                SELECT 
                    t1.job_no,
                    t1.is_freehand,
                    t1.salesman_department,
                    t1.lcl_rt,
                    t1.teu,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_booking_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
                AND t1.pro2_system_id = %s
                AND (t1.is_freehand = 0 OR (t1.is_freehand = 1 AND t1.salesman_department = '指定货业务'))
            ) ranked_data
            WHERE rn = 1
        """
        params = [begin_date, end_date, pro2_system_id]
    else:
        # 其他分公司使用窗口函数优化
        sql = """
            SELECT 
                job_no,
                COUNT(*) as nominated_count,
                SUM(lcl_rt) as nominated_rt,
                SUM(teu) as nominated_teu
            FROM (
                SELECT 
                    t1.job_no,
                    t1.lcl_rt,
                    t1.teu,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_booking_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
                AND t1.is_freehand = 0
        """
        params = [begin_date, end_date]
        
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
            GROUP BY job_no
        """
    
    async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
        async with connection.cursor(aiomysql.DictCursor) as cursor:
            await cursor.execute(sql, params)
            results = await cursor.fetchall()
    
    # 构建Job编号到指定货数据的映射
    job_nominated_map = {}
    
    if pro2_system_id == 86021:
        # 对86021，由于SQL已经过滤了正确的指定货记录，直接统计即可
        job_data = {}
        for result in results:
            job_no = result['job_no']
            if job_no not in job_data:
                job_data[job_no] = []
            job_data[job_no].append(result)
        
        # 对每个Job汇总指定货数据
        for job_no, records in job_data.items():
            nominated_count = len(records)  # 所有记录都是指定货
            nominated_rt = sum(float(record.get('lcl_rt', 0) or 0) for record in records)
            nominated_teu = sum(float(record.get('teu', 0) or 0) for record in records)
            
            if nominated_count > 0:
                job_nominated_map[job_no] = {
                    'nominated_count': nominated_count,
                    'nominated_rt': nominated_rt,
                    'nominated_teu': nominated_teu
                }
    else:
        # 其他分公司直接使用查询结果
        for result in results:
            job_no = result['job_no']
            job_nominated_map[job_no] = {
                'nominated_count': result['nominated_count'] or 0,
                'nominated_rt': float(result['nominated_rt'] or 0),
                'nominated_teu': float(result['nominated_teu'] or 0)
            }
    
    return job_nominated_map

# 工具函数：从数据库表提取海运空运损益数据（含转运）- 不带缓存
async def get_booking_details_from_tokens_table(begin_date: str, end_date: str, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    从 mcp_tokens.t_booking_details 表根据时间周期查询海运空运损益并添加转运利润字段
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD'
        end_date: 结束日期, 格式为'YYYY-MM-DD'
        pro2_system_id: 分公司代码, 可选值为: 86532-QDO(青岛), 86021-SHA(上海), 852-HKG(香港), 8103-TKY(东京), 如果为空则提取所有分公司
        
    Returns:
        Dict[str, Any]: 包含数据列表和查询信息的字典
    """
    try:
        print(f"开始从tokens表查询booking数据（含转运）: {begin_date} 到 {end_date}, 分公司: {pro2_system_id or '全部'}")
        
        # 使用窗口函数优化SQL查询，替代相关子查询提高性能
        sql = """
            SELECT 
                job_type_cn,
                job_date,
                job_no,
                bkbl_no,
                client_name,
                vessel,
                voyage,
                job_pol,
                bill_pol,
                bill_pod,
                service_mode,
                lcl_rt,
                teu,
                air_weight,
                income,
                cost,
                profit,
                transhipment_profit,
                total_business_profit,
                is_freehand,
                salesman_name,
                salesman_id,
                salesman_department,
                operator_name,
                operator_department,
                coloader_name,
                job_handling_agent,
                bl_handling_agent,
                is_transhipment,
                transhipment_id,
                bkbl_id,
                job_id,
                job_type_id,
                operator_id,
                pro2_system_id,
                data_hash
            FROM (
                SELECT 
                    t1.job_type_cn,
                    t1.job_date,
                    t1.job_no,
                    t1.bkbl_no,
                    t1.client_name,
                    t1.vessel,
                    t1.voyage,
                    t1.job_pol,
                    t1.bill_pol,
                    t1.bill_pod,
                    t1.service_mode,
                    t1.lcl_rt,
                    t1.teu,
                    t1.air_weight,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.transhipment_profit,
                    t1.total_business_profit,
                    t1.is_freehand,
                    t1.salesman_name,
                    t1.salesman_id,
                    t1.salesman_department,
                    t1.operator_name,
                    t1.operator_department,
                    t1.coloader_name,
                    t1.job_handling_agent,
                    t1.bl_handling_agent,
                    t1.is_transhipment,
                    t1.transhipment_id,
                    t1.bkbl_id,
                    t1.job_id,
                    t1.job_type_id,
                    t1.operator_id,
                    t1.pro2_system_id,
                    t1.data_hash,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_booking_details t1
                WHERE t1.job_date >= %s 
                AND t1.job_date <= %s
        """
        
        params = [begin_date, end_date]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
            ORDER BY job_date, job_no, bkbl_no
        """
        
        print("执行高性能数据库查询...")
        # 执行查询
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                raw_records = await cursor.fetchall()
        
        print(f"数据库查询完成，获得 {len(raw_records)} 条记录")
        
        # 处理特例情况
        processed_records = []
        for record in raw_records:
            processed_record = apply_special_case_processing(record)
            processed_records.append(processed_record)
        
        print(f"从tokens表查询booking数据完成（含转运）: {len(processed_records)} 条记录")
        
        return {
            'data': processed_records,
            'total_count': len(processed_records),
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部订舱毛利数据（含转运）- 从tokens表提取',
                'source_table': 'mcp_tokens.t_booking_details',
                'pro2_system_id': pro2_system_id,
                'system_filter': f'分公司代码: {pro2_system_id}' if pro2_system_id else '全部分公司'
            }
        }
        
    except Exception as e:
        print(f"从tokens表查询booking数据失败（含转运）: {e}")
        import traceback
        traceback.print_exc()
        return {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部订舱毛利数据（含转运）- 从tokens表提取',
                'source_table': 'mcp_tokens.t_booking_details',
                'pro2_system_id': pro2_system_id,
                'system_filter': f'分公司代码: {pro2_system_id}' if pro2_system_id else '全部分公司',
                'error': f'查询失败: {str(e)}'
            }
        }

# 工具函数：从数据库表提取作业明细数据（含转运）- 不带缓存
async def get_job_details_from_tokens_table(begin_date: str, end_date: str, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    从 mcp_tokens.t_job_details 表根据时间周期查询作业明细并添加转运利润字段
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD'
        end_date: 结束日期, 格式为'YYYY-MM-DD'
        pro2_system_id: 分公司代码, 可选值为: 86532-QDO(青岛), 86021-SHA(上海), 852-HKG(香港), 8103-TKY(东京), 如果为空则提取所有分公司
        
    Returns:
        Dict[str, Any]: 包含数据列表和查询信息的字典
    """
    try:
        print(f"开始从tokens表查询job数据（含转运）: {begin_date} 到 {end_date}, 分公司: {pro2_system_id or '全部'}")
        
        # 使用窗口函数优化SQL查询，替代相关子查询提高性能
        sql = """
            SELECT 
                job_type_cn,
                job_date,
                job_no,
                vessel,
                voyage,
                pol_code,
                pod_code,
                bk_count,
                rt,
                teu,
                income,
                cost,
                profit,
                transhipment_profit,
                total_business_profit,
                operator_name,
                job_handling_agent,
                nomi_count,
                nomi_rt,
                is_consolidation,
                bill_count,
                consolidation_20,
                consolidation_40,
                operator_department,
                is_op_finished,
                is_checked,
                job_id,
                job_type_id,
                operator_id,
                etd_date,
                eta_date,
                pro2_system_id,
                data_hash
            FROM (
                SELECT 
                    t1.job_type_cn,
                    t1.job_date,
                    t1.job_no,
                    t1.vessel,
                    t1.voyage,
                    t1.pol_code,
                    t1.pod_code,
                    t1.bk_count,
                    t1.rt,
                    t1.teu,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.transhipment_profit,
                    t1.total_business_profit,
                    t1.operator_name,
                    t1.job_handling_agent,
                    t1.nomi_count,
                    t1.nomi_rt,
                    t1.is_consolidation,
                    t1.bill_count,
                    t1.consolidation_20,
                    t1.consolidation_40,
                    t1.operator_department,
                    t1.is_op_finished,
                    t1.is_checked,
                    t1.job_id,
                    t1.job_type_id,
                    t1.operator_id,
                    t1.etd_date,
                    t1.eta_date,
                    t1.pro2_system_id,
                    t1.data_hash,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_job_details t1
                WHERE t1.job_date >= %s 
                AND t1.job_date <= %s
        """
        
        params = [begin_date, end_date]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
            ORDER BY job_date, job_no
        """
        
        print("执行高性能数据库查询...")
        # 执行查询
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                raw_records = await cursor.fetchall()
        
        print(f"数据库查询完成，获得 {len(raw_records)} 条记录")
        
        # 处理特例情况
        processed_records = []
        for record in raw_records:
            processed_record = apply_special_case_processing_job(record)
            processed_records.append(processed_record)
        
        # 对于86021，需要从Booking表重新获取指定货数据
        if pro2_system_id == 86021 and processed_records:
            print("正在从Booking表获取86021指定货数据...")
            job_nominated_map = await get_job_nominated_data_from_booking(begin_date, end_date, pro2_system_id)
            
            # 更新Job记录中的指定货数据
            for record in processed_records:
                job_no = record.get('job_no', '')
                if job_no in job_nominated_map:
                    nominated_data = job_nominated_map[job_no]
                    record['nomi_count'] = nominated_data['nominated_count']
                    record['nomi_rt'] = nominated_data['nominated_rt']
                    # 如果需要，也可以更新nomi_teu
                    if 'nominated_teu' in nominated_data:
                        record['nomi_teu'] = nominated_data['nominated_teu']
            
            print(f"86021指定货数据更新完成，共更新 {len(job_nominated_map)} 个Job")
        
        # 如果没有查询到数据
        if not processed_records:
            print(f"从tokens表未查询到job数据: {begin_date} 到 {end_date}")
            return {
                'data': [],
                'total_count': 0,
                'query_info': {
                    'date_range': f'{begin_date} 到 {end_date}',
                    'data_type': '全部作业明细数据（含转运）- 从tokens表提取',
                    'source_table': 'mcp_tokens.t_job_details',
                    'pro2_system_id': pro2_system_id,
                    'system_filter': f'分公司代码: {pro2_system_id}' if pro2_system_id else '全部分公司',
                    'message': '未查询到数据'
                }
            }
        
        print(f"从tokens表查询job数据完成（含转运）: {len(processed_records)} 条记录")
        
        return {
            'data': processed_records,
            'total_count': len(processed_records),
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部作业明细数据（含转运）- 从tokens表提取',
                'source_table': 'mcp_tokens.t_job_details',
                'pro2_system_id': pro2_system_id,
                'system_filter': f'分公司代码: {pro2_system_id}' if pro2_system_id else '全部分公司'
            }
        }
        
    except Exception as e:
        print(f"从tokens表查询job数据失败（含转运）: {e}")
        import traceback
        traceback.print_exc()
        return {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部作业明细数据（含转运）- 从tokens表提取',
                'source_table': 'mcp_tokens.t_job_details',
                'pro2_system_id': pro2_system_id,
                'system_filter': f'分公司代码: {pro2_system_id}' if pro2_system_id else '全部分公司',
                'error': f'查询失败: {str(e)}'
            }
        }

# 辅助函数: 计算记录统计数据
def _calculate_records_stats(records: List[Dict[str, Any]]) -> Dict[str, Any]:
    """计算记录统计数据的辅助函数"""
    if not records:
        return {
            "customer_count": 0,
            "bkbl_count": 0,
            "rt": 0,
            "teu": 0,
            "air_weight": 0,
            "income": 0,
            "cost": 0,
            "profit": 0,
            "profit_rate": 0,
        }
    
    # 计算客户数量（去重）
    customers = set()
    for r in records:
        client_name = r.get('client_name', '')
        if client_name and client_name.strip():
            customers.add(client_name.strip())
    
    total_income = sum(float(r.get('income', 0) or 0) for r in records)
    total_cost = abs(sum(float(r.get('cost', 0) or 0) for r in records))
    total_profit = sum(float(r.get('profit', 0) or 0) for r in records)
    profit_rate = (total_profit / total_income * 100) if total_income > 0 else 0
    
    return {
        "customer_count": len(customers),
        "bkbl_count": len(records),
        "rt": sum(float(r.get('lcl_rt', 0) or 0) for r in records),
        "teu": sum(float(r.get('teu', 0) or 0) for r in records),
        "air_weight": sum(float(r.get('air_weight', 0) or 0) for r in records),
        "income": total_income,
        "cost": total_cost,
        "profit": total_profit,
        "profit_rate": round(profit_rate, 2),
    }

# 辅助函数: 计算指定货统计数据
def _calculate_nominated_stats(records: List[Dict[str, Any]]) -> Dict[str, Any]:
    """计算指定货统计数据的辅助函数"""
    if not records:
        return {
            "all_nominated_customer_count": 0,
            "all_nominated_count": 0,
            "all_nominated_rt": 0,
            "all_nominated_teu": 0,
            "all_nominated_air_weight": 0,
            "all_nominated_income": 0,
            "all_nominated_cost": 0,
            "all_nominated_profit": 0,
            "all_nominated_profit_rate": 0,
        }
    
    customers = set()
    for r in records:
        client_name = r.get('client_name', '')
        if client_name and client_name.strip():
            customers.add(client_name.strip())
    
    income = sum(float(r.get('income', 0) or 0) for r in records)
    cost = abs(sum(float(r.get('cost', 0) or 0) for r in records))
    profit = sum(float(r.get('profit', 0) or 0) for r in records)
    profit_rate = (profit / income * 100) if income > 0 else 0
    
    return {
        "all_nominated_customer_count": len(customers),
        "all_nominated_count": len(records),
        "all_nominated_rt": sum(float(r.get('lcl_rt', 0) or 0) for r in records),
        "all_nominated_teu": sum(float(r.get('teu', 0) or 0) for r in records),
        "all_nominated_air_weight": sum(float(r.get('air_weight', 0) or 0) for r in records),
        "all_nominated_income": income,
        "all_nominated_cost": cost,
        "all_nominated_profit": profit,
        "all_nominated_profit_rate": round(profit_rate, 2),
    }

#########################################################################
# 常用分析场景
#########################################################################

# 常用分析场景1: 分析连续n月每个月的数据(Booking分析）- 检查ok
async def analysis_booking_by_month_data(begin_date: str, month_count: int, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    高性能优化版本: 分析连续n月每个月的数据(Booking分析)
    
    主要优化:
    1. 使用窗口函数替代相关子查询，大幅提升SQL查询性能
    2. 一次性查询整个时间范围的数据，避免多次数据库查询
    3. 在内存中进行月份和业务类型分组，减少数据库负载
    4. 正确处理86021特例规则
    5. 修正：完全对齐原始版本的查询逻辑，确保数据一致性
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        pro2_system_id: 分公司代码
    
    Returns:
        List[Dict], 包含连续n月每个月的数据(Booking分析），包括各业务类型的指标
    """
    try:
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期 - 使用与原始版本相同的计算方式
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"高性能查询Booking月度数据: {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 使用窗口函数优化的SQL查询 - 完全对齐原始版本的查询逻辑
        # 重要修正：移除所有额外的过滤条件，完全匹配原始版本
        sql = """
            SELECT 
                job_type_cn,
                job_date,
                job_no,
                bkbl_no,
                client_name,
                vessel,
                voyage,
                job_pol,
                bill_pol,
                bill_pod,
                service_mode,
                lcl_rt,
                teu,
                air_weight,
                income,
                cost,
                profit,
                transhipment_profit,
                total_business_profit,
                is_freehand,
                salesman_name,
                salesman_department,
                operator_name,
                operator_department,
                coloader_name,
                job_handling_agent,
                bl_handling_agent,
                is_transhipment,
                pro2_system_id
            FROM (
                SELECT 
                    t1.job_type_cn,
                    t1.job_date,
                    t1.job_no,
                    t1.bkbl_no,
                    t1.client_name,
                    t1.vessel,
                    t1.voyage,
                    t1.job_pol,
                    t1.bill_pol,
                    t1.bill_pod,
                    t1.service_mode,
                    t1.lcl_rt,
                    t1.teu,
                    t1.air_weight,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.transhipment_profit,
                    t1.total_business_profit,
                    t1.is_freehand,
                    t1.salesman_name,
                    t1.salesman_department,
                    t1.operator_name,
                    t1.operator_department,
                    t1.coloader_name,
                    t1.job_handling_agent,
                    t1.bl_handling_agent,
                    t1.is_transhipment,
                    t1.pro2_system_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_booking_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
        """
        
        params = [begin_date, end_date.strftime('%Y-%m-%d')]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
        """
        
        print("执行高性能数据库查询...")
        print(f"SQL查询参数: {params}")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                all_records = await cursor.fetchall()
        
        print(f"查询完成，获得 {len(all_records)} 条记录")
        
        if not all_records:
            # 如果没有数据，返回空结果结构
            results = []
            current_date = start_date
            for month_offset in range(month_count):
                month_data = {
                    "year": f"{current_date.year}",
                    "month": f"{current_date.month:02d}",
                    "pro2_system_id": pro2_system_id,
                    "sea_export_data": [calculate_aggregated_data([])],
                    "sea_import_data": [calculate_aggregated_data([])],
                    "triangle_trade_data": [calculate_aggregated_data([])],
                    "air_data": []
                }
                # 空运数据移除TEU相关字段
                air_data = calculate_aggregated_data([]).copy()
                air_data.pop('teu', None)
                air_data.pop('all_nominated_rt', None)
                month_data["air_data"] = [air_data]
                
                results.append(month_data)
                current_date = current_date + relativedelta(months=1)
            return results
        
        # 应用特例处理并按月份和业务类型分组
        monthly_business_data = defaultdict(lambda: defaultdict(list))
        
        for record in all_records:
            # 应用特例处理
            processed_record = apply_special_case_processing(record)
            
            job_date = processed_record['job_date']
            year_month = f"{job_date.year}-{job_date.month:02d}"
            business_type = processed_record.get('job_type_cn', '')
            
            # 按月份和业务类型分组数据
            monthly_business_data[year_month][business_type].append(processed_record)
        
        print(f"数据按月分组完成，共处理 {len(monthly_business_data)} 个月份的数据")
        
        # 构建结果
        results = []
        current_date = start_date
        
        for month_offset in range(month_count):
            year_month = f"{current_date.year}-{current_date.month:02d}"
            
            # 获取该月的数据
            business_data_dict = monthly_business_data.get(year_month, {})
            
            month_data = {
                "year": f"{current_date.year}",
                "month": f"{current_date.month:02d}",
                "pro2_system_id": pro2_system_id,
                "sea_export_data": [],
                "sea_import_data": [],
                "triangle_trade_data": [],
                "air_data": []
            }
            
            # 调试输出
            month_record_count = sum(len(records) for records in business_data_dict.values())
            print(f"第{month_offset+1}月 ({year_month}) 的记录数: {month_record_count}")
            for business_type, records in business_data_dict.items():
                print(f"  {business_type}: {len(records)} 条记录")
            
            # 计算各业务类型的汇总数据
            for business_type in ['海运出口', '海运进口', '海运三角贸易', '空运']:
                records = business_data_dict.get(business_type, [])
                aggregated_data = calculate_aggregated_data(records)
                
                if business_type == '海运出口':
                    month_data["sea_export_data"].append(aggregated_data)
                elif business_type == '海运进口':
                    month_data["sea_import_data"].append(aggregated_data)
                elif business_type == '海运三角贸易':
                    month_data["triangle_trade_data"].append(aggregated_data)
                elif business_type == '空运':
                    # 空运数据不包括TEU
                    air_data = aggregated_data.copy()
                    air_data.pop('teu', None)
                    air_data.pop('all_nominated_rt', None)  # 空运不计算指定货RT
                    month_data["air_data"].append(air_data)
            
            # 如果某个业务类型没有数据，确保数组不为空
            for data_key in ["sea_export_data", "sea_import_data", "triangle_trade_data", "air_data"]:
                if not month_data[data_key]:
                    empty_data = calculate_aggregated_data([])
                    if data_key == "air_data":
                        empty_data.pop('teu', None)
                        empty_data.pop('all_nominated_rt', None)
                    month_data[data_key].append(empty_data)
            
            results.append(month_data)
            current_date = current_date + relativedelta(months=1)
        
        # 转换为DataFrame格式
        columns_info = {
            "year": "年份",
            "month": "月份", 
            "pro2_system_id": "分公司代码",
            "business_type": "业务类型",
            "customer_count": "客户数量",
            "bkbl_count": "票数",
            "rt": "计费吨",
            "teu": "TEU",
            "air_weight": "空运重量",
            "income": "收入",
            "cost": "成本", 
            "profit": "利润",
            "profit_rate": "利润率(%)",
            "all_nominated_count": "指定货票数",
            "all_nominated_rt": "指定货RT",
            "all_nominated_profit": "指定货利润",
            "nomi_agent_count": "指定货代理数量"
        }
        df_data = convert_monthly_analysis_to_dataframe(results, 'booking', columns_info)
        
        return {
            "json_data": results,
            "pd_data": df_data,
            "metadata": {
                "data_type": "booking_monthly_analysis",
                "analysis_period": f"{begin_date} to {end_date.strftime('%Y-%m-%d')}",
                "month_count": month_count,
                "pro2_system_id": pro2_system_id,
                "total_months": len(results),
                "columns_info": {
                    "year": "年份",
                    "month": "月份", 
                    "pro2_system_id": "分公司代码",
                    "business_type": "业务类型",
                    "customer_count": "客户数量",
                    "bkbl_count": "票数",
                    "rt": "计费吨",
                    "teu": "TEU",
                    "air_weight": "空运重量",
                    "income": "收入",
                    "cost": "成本", 
                    "profit": "利润",
                    "profit_rate": "利润率(%)",
                    "all_nominated_count": "指定货票数",
                    "all_nominated_rt": "指定货RT",
                    "all_nominated_profit": "指定货利润",
                    "nomi_agent_count": "指定货代理数量"
                }
            }
        }
        
    except Exception as e:
        print(f"高性能分析Booking月度数据时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "json_data": [],
            "pd_data": pd.DataFrame(),
            "metadata": {
                "data_type": "booking_monthly_analysis",
                "error": str(e),
                "analysis_period": f"{begin_date} (error occurred)",
                "month_count": month_count,
                "pro2_system_id": pro2_system_id
            }
        }

# 常用分析场景2: 分析连续n月每个月的数据(Job分析）- 检查ok
async def analysis_job_by_month_data(begin_date: str, month_count: int, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    高性能优化版本: 分析连续n月每个月的数据(Job分析)
    
    主要优化:
    1. 使用窗口函数替代相关子查询，大幅提升SQL查询性能
    2. 一次性查询整个时间范围的数据，避免多次数据库查询
    3. 优化86021特例处理，合并Job和Booking数据获取
    4. 在内存中进行月份和业务类型分组，减少数据库负载
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        pro2_system_id: 分公司代码
    
    Returns:
        List[Dict], 包含连续n月每个月的数据(Job分析）
    """
    try:
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"高性能查询Job月度数据: {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 使用窗口函数优化的SQL查询Job表
        job_sql = """
            SELECT 
                job_type_cn,
                job_date,
                job_no,
                vessel,
                voyage,
                pol_code,
                pod_code,
                bk_count,
                rt,
                teu,
                income,
                cost,
                profit,
                transhipment_profit,
                total_business_profit,
                operator_name,
                job_handling_agent,
                nomi_count,
                nomi_rt,
                is_consolidation,
                bill_count,
                consolidation_20,
                consolidation_40,
                operator_department,
                is_op_finished,
                is_checked,
                pro2_system_id
            FROM (
                SELECT 
                    t1.job_type_cn,
                    t1.job_date,
                    t1.job_no,
                    t1.vessel,
                    t1.voyage,
                    t1.pol_code,
                    t1.pod_code,
                    t1.bk_count,
                    t1.rt,
                    t1.teu,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.transhipment_profit,
                    t1.total_business_profit,
                    t1.operator_name,
                    t1.job_handling_agent,
                    t1.nomi_count,
                    t1.nomi_rt,
                    t1.is_consolidation,
                    t1.bill_count,
                    t1.consolidation_20,
                    t1.consolidation_40,
                    t1.operator_department,
                    t1.is_op_finished,
                    t1.is_checked,
                    t1.pro2_system_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_job_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
        """
        
        job_params = [begin_date, end_date.strftime('%Y-%m-%d')]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            job_sql += " AND t1.pro2_system_id = %s"
            job_params.append(pro2_system_id)
        
        job_sql += """
            ) ranked_data
            WHERE rn = 1
        """
        
        print("执行高性能Job数据库查询...")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(job_sql, job_params)
                job_records = await cursor.fetchall()
        
        print(f"Job查询完成，获得 {len(job_records)} 条记录")
        
        # 对于86021，需要优化的方式获取指定货数据
        job_nominated_map = {}
        if pro2_system_id == 86021 or (pro2_system_id is None and job_records):
            print("查询86021指定货数据...")
            
            # 确定需要查询的分公司
            target_pro2_system_id = 86021 if pro2_system_id == 86021 else None
            
            # 使用窗口函数优化的Booking查询
            booking_sql = """
                SELECT 
                    job_no,
                    is_freehand,
                    salesman_department,
                    lcl_rt,
                    teu,
                    pro2_system_id
                FROM (
                    SELECT 
                        t1.job_no,
                        t1.is_freehand,
                        t1.salesman_department,
                        t1.lcl_rt,
                        t1.teu,
                        t1.pro2_system_id,
                        ROW_NUMBER() OVER (
                            PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                            ORDER BY t1.id DESC
                        ) as rn
                    FROM t_booking_details t1
                    WHERE t1.job_date >= %s AND t1.job_date <= %s
            """
            
            booking_params = [begin_date, end_date.strftime('%Y-%m-%d')]
            
            if target_pro2_system_id is not None:
                booking_sql += " AND t1.pro2_system_id = %s"
                booking_params.append(target_pro2_system_id)
            else:
                # 如果没有指定分公司，只查询86021的数据
                booking_sql += " AND t1.pro2_system_id = 86021"
            
            booking_sql += """
                ) ranked_data
                WHERE rn = 1
            """
            
            async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
                async with connection.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(booking_sql, booking_params)
                    booking_records = await cursor.fetchall()
            
            print(f"Booking查询完成，获得 {len(booking_records)} 条记录")
            
            # 处理86021特例并汇总指定货数据
            job_data = defaultdict(list)
            for record in booking_records:
                if record.get('pro2_system_id') == 86021:
                    # 应用特例处理
                    processed_record = apply_special_case_processing(record)
                    job_no = record['job_no']
                    job_data[job_no].append(processed_record)
                else:
                    # 其他分公司直接处理
                    job_no = record['job_no']
                    job_data[job_no].append(record)
            
            # 对每个Job汇总指定货数据
            for job_no, records in job_data.items():
                nominated_count = 0
                nominated_rt = 0
                nominated_teu = 0
                
                for record in records:
                    # 如果是指定货（is_freehand=0），则计入统计
                    if record.get('is_freehand') == 0:
                        nominated_count += 1
                        nominated_rt += float(record.get('lcl_rt', 0) or 0)
                        nominated_teu += float(record.get('teu', 0) or 0)
                
                if nominated_count > 0:
                    job_nominated_map[job_no] = {
                        'nominated_count': nominated_count,
                        'nominated_rt': nominated_rt,
                        'nominated_teu': nominated_teu
                    }
        
        if not job_records:
            # 如果没有数据，返回空结果结构
            results = []
            current_date = start_date
            for month_offset in range(month_count):
                month_data = {
                    "year": f"{current_date.year}",
                    "month": f"{current_date.month:02d}",
                    "pro2_system_id": pro2_system_id,
                    "sea_export_data": [calculate_job_aggregated_data([], "海运出口")],
                    "sea_import_data": [calculate_job_aggregated_data([], "海运进口")],
                    "triangle_trade_data": [calculate_job_aggregated_data([], "海运三角贸易")],
                    "air_data": []
                }
                # 空运数据移除相关字段
                air_data = calculate_job_aggregated_data([], "空运")
                for key in ['teu', 'all_nominated_rt', 'all_nominated_teu', 'consol_line_count', 'consol_rt', 'consol_20_count', 'consol_40_count']:
                    air_data.pop(key, None)
                month_data["air_data"].append(air_data)
                
                results.append(month_data)
                current_date = current_date + relativedelta(months=1)
            return results
        
        # 应用特例处理并按月份和业务类型分组
        monthly_business_data = defaultdict(lambda: defaultdict(list))
        
        for record in job_records:
            # 应用特例处理
            processed_record = apply_special_case_processing_job(record)
            
            # 如果是86021的数据，更新指定货信息
            if processed_record.get('pro2_system_id') == 86021:
                job_no = processed_record.get('job_no', '')
                if job_no in job_nominated_map:
                    nominated_data = job_nominated_map[job_no]
                    processed_record['nomi_count'] = nominated_data['nominated_count']
                    processed_record['nomi_rt'] = nominated_data['nominated_rt']
                    processed_record['nomi_teu'] = nominated_data['nominated_teu']
            
            job_date = processed_record['job_date']
            year_month = f"{job_date.year}-{job_date.month:02d}"
            business_type = processed_record.get('job_type_cn', '')
            
            # 按月份和业务类型分组数据
            monthly_business_data[year_month][business_type].append(processed_record)
        
        # 构建结果
        results = []
        current_date = start_date
        
        for month_offset in range(month_count):
            year_month = f"{current_date.year}-{current_date.month:02d}"
            
            # 获取该月的数据
            business_data_dict = monthly_business_data.get(year_month, {})
            
            month_data = {
                "year": f"{current_date.year}",
                "month": f"{current_date.month:02d}",
                "pro2_system_id": pro2_system_id,
                "sea_export_data": [],
                "sea_import_data": [],
                "triangle_trade_data": [],
                "air_data": []
            }
            
            # 计算各业务类型的汇总数据
            for business_type in ['海运出口', '海运进口', '海运三角贸易', '空运']:
                records = business_data_dict.get(business_type, [])
                aggregated_data = calculate_job_aggregated_data(records, business_type)
                
                if business_type == '海运出口':
                    month_data["sea_export_data"].append(aggregated_data)
                elif business_type == '海运进口':
                    month_data["sea_import_data"].append(aggregated_data)
                elif business_type == '海运三角贸易':
                    month_data["triangle_trade_data"].append(aggregated_data)
                elif business_type == '空运':
                    # 空运数据不包括TEU和集拼相关数据
                    air_data = aggregated_data.copy()
                    for key in ['teu', 'all_nominated_rt', 'all_nominated_teu', 'consol_line_count', 'consol_rt', 'consol_20_count', 'consol_40_count']:
                        air_data.pop(key, None)
                    month_data["air_data"].append(air_data)
            
            # 如果某个业务类型没有数据，确保数组不为空
            for data_key in ["sea_export_data", "sea_import_data", "triangle_trade_data", "air_data"]:
                if not month_data[data_key]:
                    if data_key == "sea_export_data":
                        empty_business_type = "海运出口"
                    elif data_key == "sea_import_data":
                        empty_business_type = "海运进口"
                    elif data_key == "triangle_trade_data":
                        empty_business_type = "海运三角贸易"
                    else:
                        empty_business_type = "空运"
                    
                    empty_data = calculate_job_aggregated_data([], empty_business_type)
                    if data_key == "air_data":
                        for key in ['teu', 'all_nominated_rt', 'all_nominated_teu', 'consol_line_count', 'consol_rt', 'consol_20_count', 'consol_40_count']:
                            empty_data.pop(key, None)
                    month_data[data_key].append(empty_data)
            
            results.append(month_data)
            current_date = current_date + relativedelta(months=1)
        
        # 转换为DataFrame格式
        columns_info = {
            "year": "年份",
            "month": "月份", 
            "pro2_system_id": "分公司代码",
            "business_type": "业务类型",
            "bkbl_count": "票数",
            "rt": "计费吨",
            "teu": "TEU",
            "income": "收入",
            "cost": "成本", 
            "profit": "利润",
            "profit_rate": "利润率(%)",
            "all_nominated_count": "指定货票数",
            "all_nominated_rt": "指定货RT",
            "all_nominated_teu": "指定货TEU",
            "consol_line_count": "集拼航线数",
            "consol_rt": "集拼RT",
            "consol_20_count": "20尺集拼量",
            "consol_40_count": "40尺集拼量"
        }
        df_data = convert_monthly_analysis_to_dataframe(results, 'job', columns_info)
        
        return {
            "json_data": results,
            "pd_data": df_data,
            "metadata": {
                "data_type": "job_monthly_analysis",
                "analysis_period": f"{begin_date} to {end_date.strftime('%Y-%m-%d')}",
                "month_count": month_count,
                "pro2_system_id": pro2_system_id,
                "total_months": len(results),
                "columns_info": {
                    "year": "年份",
                    "month": "月份", 
                    "pro2_system_id": "分公司代码",
                    "business_type": "业务类型",
                    "bkbl_count": "票数",
                    "rt": "计费吨",
                    "teu": "TEU",
                    "income": "收入",
                    "cost": "成本", 
                    "profit": "利润",
                    "profit_rate": "利润率(%)",
                    "all_nominated_count": "指定货票数",
                    "all_nominated_rt": "指定货RT",
                    "all_nominated_teu": "指定货TEU",
                    "consol_line_count": "集拼航线数",
                    "consol_rt": "集拼RT",
                    "consol_20_count": "20尺集拼量",
                    "consol_40_count": "40尺集拼量"
                }
            }
        }
        
    except Exception as e:
        print(f"高性能分析Job月度数据时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "json_data": [],
            "pd_data": pd.DataFrame(),
            "metadata": {
                "data_type": "job_monthly_analysis",
                "error": str(e),
                "analysis_period": f"{begin_date} (error occurred)",
                "month_count": month_count,
                "pro2_system_id": pro2_system_id
            }
        }

# 常用分析场景3: 分析连续n个月集拼航线的数据(Job分析) - 检查ok
async def analysis_job_consol_line(begin_date: str, month_count: int, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    优化版本: 分析连续n个月集拼航线的数据(Job分析)
    
    主要优化:
    1. 一次性查询所有数据，避免嵌套循环查询
    2. 在内存中进行分组统计，而不是多次查询数据库
    3. 简化SQL查询，只获取必要字段
    4. 对86021特例正确处理指定货数据，从Booking表重新计算
    """
    try:
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"优化查询集拼航线: {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 一次性查询所有集拼数据，添加job_no字段用于后续指定货数据关联
        sql = """
            SELECT 
                t1.job_type_cn,
                t1.job_date,
                t1.job_no,
                t1.pol_code,
                t1.pod_code,
                t1.bk_count,
                t1.bill_count,
                t1.rt,
                t1.income,
                t1.cost,
                t1.profit,
                t1.consolidation_20,
                t1.consolidation_40,
                t1.nomi_count,
                t1.nomi_rt,
                t1.pro2_system_id
            FROM t_job_details t1
            WHERE t1.job_date >= %s AND t1.job_date <= %s
            AND t1.is_consolidation = 1
            AND t1.pol_code IS NOT NULL 
            AND t1.pod_code IS NOT NULL
            AND t1.pol_code != ''
            AND t1.pod_code != ''
            AND t1.id = (
                SELECT MAX(t2.id) 
                FROM t_job_details t2 
                WHERE t2.job_id = t1.job_id
                                AND t2.pro2_system_id = t1.pro2_system_id
            )
        """
        
        params = [begin_date, end_date.strftime('%Y-%m-%d')]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        print("执行数据库查询...")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                all_records = await cursor.fetchall()
        
        print(f"Job查询完成，获得 {len(all_records)} 条记录")
        
        # 对于86021，需要从Booking表重新获取正确的指定货数据
        job_nominated_map = {}
        if pro2_system_id == 86021 or (pro2_system_id is None and any(r.get('pro2_system_id') == 86021 for r in all_records)):
            print("查询86021指定货数据...")
            
            # 确定需要查询的分公司
            target_pro2_system_id = 86021 if pro2_system_id == 86021 else None
            
            # 获取86021的指定货数据
            job_nominated_map = await get_job_nominated_data_from_booking(
                begin_date, end_date.strftime('%Y-%m-%d'), target_pro2_system_id or 86021
            )
            
            print(f"86021指定货数据查询完成，共 {len(job_nominated_map)} 个Job")
        
        # 在内存中按集拼航线和月份分组，同时更新86021的指定货数据
        consol_data = defaultdict(lambda: defaultdict(list))
        
        for record in all_records:
            # 创建记录副本以避免修改原始数据
            processed_record = record.copy()
            
            # 如果是86021的数据，更新指定货信息
            if processed_record.get('pro2_system_id') == 86021:
                job_no = processed_record.get('job_no', '')
                if job_no in job_nominated_map:
                    nominated_data = job_nominated_map[job_no]
                    processed_record['nomi_count'] = nominated_data['nominated_count']
                    processed_record['nomi_rt'] = nominated_data['nominated_rt']
                    print(f"更新Job {job_no} 指定货数据: 票数={nominated_data['nominated_count']}, RT={nominated_data['nominated_rt']}")
                else:
                    # 如果在Booking表中没有找到对应的指定货数据，设为0
                    processed_record['nomi_count'] = 0
                    processed_record['nomi_rt'] = 0
            
            job_date = processed_record['job_date']
            year_month = f"{job_date.year}-{job_date.month:02d}"
            pol_code = processed_record['pol_code']
            pod_code = processed_record['pod_code']
            consol_line = f"{pol_code}/{pod_code}"
            
            consol_data[consol_line][year_month].append(processed_record)
        
        print(f"分组完成，找到 {len(consol_data)} 条集拼航线")
        
        # 构建结果
        results = []
        
        for consol_line, month_data in consol_data.items():
            line_result = {
                "pro2_system_id": pro2_system_id,
                "consol_line": consol_line,
                "data": []
            }
            
            # 为每个月生成数据
            current_date = start_date
            for month_offset in range(month_count):
                year_month = f"{current_date.year}-{current_date.month:02d}"
                records = month_data.get(year_month, [])
                
                # 计算汇总数据
                if records:
                    # 根据业务类型确定票数计算方式
                    business_type = records[0].get('job_type_cn', '')
                    if business_type == '海运出口':
                        total_count = sum(int(r.get('bk_count', 0) or 0) for r in records)
                    else:
                        total_count = sum(int(r.get('bill_count', 0) or 0) for r in records)
                    
                    total_income = sum(float(r.get('income', 0) or 0) for r in records)
                    total_cost = abs(sum(float(r.get('cost', 0) or 0) for r in records))
                    total_profit = sum(float(r.get('profit', 0) or 0) for r in records)
                    total_rt = sum(float(r.get('rt', 0) or 0) for r in records)
                    
                    profit_rate = (total_profit / total_income * 100) if total_income > 0 else 0
                    
                    # 计算指定货数据（现在对86021已经是正确的数据）
                    nominated_count = sum(int(r.get('nomi_count', 0) or 0) for r in records)
                    nominated_rt = sum(float(r.get('nomi_rt', 0) or 0) for r in records)
                    
                    month_result = {
                        "year": f"{current_date.year}",
                        "month": f"{current_date.month:02d}",
                        "bkbl_count": total_count,
                        "rt": total_rt,
                        "income": total_income,
                        "cost": total_cost,
                        "profit": total_profit,
                        "profit_rate": round(profit_rate, 2),
                        "consol_rt": total_rt,  # 集拼数据的RT就是总RT
                        "consol_20_count": sum(int(r.get('consolidation_20', 0) or 0) for r in records),
                        "consol_40_count": sum(int(r.get('consolidation_40', 0) or 0) for r in records),
                        "all_nominated_count": nominated_count,
                        "all_nominated_rt": nominated_rt,
                        "port_nominated_count": 0,  # 暂不实现
                        "port_nominated_rt": 0,     # 暂不实现
                    }
                else:
                    # 空数据
                    month_result = {
                        "year": f"{current_date.year}",
                        "month": f"{current_date.month:02d}",
                        "bkbl_count": 0,
                        "rt": 0,
                        "income": 0,
                        "cost": 0,
                        "profit": 0,
                        "profit_rate": 0,
                        "consol_rt": 0,
                        "consol_20_count": 0,
                        "consol_40_count": 0,
                        "all_nominated_count": 0,
                        "all_nominated_rt": 0,
                        "port_nominated_count": 0,
                        "port_nominated_rt": 0,
                    }
                
                line_result["data"].append(month_result)
                current_date = current_date + relativedelta(months=1)
            
            results.append(line_result)
        
        # 按集拼航线名称排序
        results.sort(key=lambda x: x["consol_line"])
        
        # 转换为DataFrame格式
        columns_info = {
            "consol_line": "集拼航线",
            "year": "年份",
            "month": "月份",
            "pro2_system_id": "分公司代码",
            "entity_type": "实体类型",
            "bkbl_count": "票数",
            "rt": "计费吨",
            "income": "收入",
            "cost": "成本",
            "profit": "利润",
            "profit_rate": "利润率(%)",
            "consol_rt": "集拼RT",
            "consol_20_count": "20尺集拼量",
            "consol_40_count": "40尺集拼量",
            "all_nominated_count": "指定货票数",
            "all_nominated_rt": "指定货RT",
            "port_nominated_count": "港代指定货票数",
            "port_nominated_rt": "港代指定货RT"
        }
        df_data = convert_entity_analysis_to_dataframe(results, 'consol_line', columns_info)
        
        return {
            "json_data": results,
            "pd_data": df_data,
            "metadata": {
                "data_type": "consol_line_analysis",
                "analysis_period": f"{begin_date} to {end_date.strftime('%Y-%m-%d')}",
                "month_count": month_count,
                "pro2_system_id": pro2_system_id,
                "total_lines": len(results),
                "columns_info": {
                    "consol_line": "集拼航线",
                    "year": "年份",
                    "month": "月份",
                    "pro2_system_id": "分公司代码",
                    "entity_type": "实体类型",
                    "bkbl_count": "票数",
                    "rt": "计费吨",
                    "income": "收入",
                    "cost": "成本",
                    "profit": "利润",
                    "profit_rate": "利润率(%)",
                    "consol_rt": "集拼RT",
                    "consol_20_count": "20尺集拼量",
                    "consol_40_count": "40尺集拼量",
                    "all_nominated_count": "指定货票数",
                    "all_nominated_rt": "指定货RT",
                    "port_nominated_count": "港代指定货票数",
                    "port_nominated_rt": "港代指定货RT"
                }
            }
        }
        
    except Exception as e:
        print(f"优化分析集拼航线数据时发生错误: {str(e)}")
        traceback.print_exc()
        return {
            "json_data": [],
            "pd_data": pd.DataFrame(),
            "metadata": {
                "data_type": "consol_line_analysis",
                "error": str(e),
                "analysis_period": f"{begin_date} (error occurred)",
                "month_count": month_count,
                "pro2_system_id": pro2_system_id
            }
        }

# 常用分析场景4: 分析连续n月每个月某客户的数据(Booking分析）- 检查ok
async def analysis_booking_by_customer(begin_date: str, month_count: int, customer_name: str, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    高性能优化版本: 分析连续n月每个月指定客户的数据(Booking分析)
    
    主要优化:
    1. 使用窗口函数替代相关子查询，大幅提升SQL查询性能
    2. 一次性查询整个时间范围的数据，避免多次数据库查询
    3. 在内存中进行月份和目的港分组，减少数据库负载
    4. 正确处理86021特例规则
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        customer_name: 客户名称
        pro2_system_id: 分公司代码
    
    Returns:
        List[Dict], 包含连续n月每个月指定客户的数据，按卸货港分组
    """
    try:
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"高性能查询客户 '{customer_name}': {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 使用窗口函数优化的SQL查询
        sql = """
            SELECT 
                job_date,
                job_type_cn,
                job_no,
                bkbl_no,
                client_name,
                vessel,
                voyage,
                job_pol,
                bill_pol,
                bill_pod,
                service_mode,
                lcl_rt,
                teu,
                air_weight,
                income,
                cost,
                profit,
                transhipment_profit,
                total_business_profit,
                is_freehand,
                salesman_name,
                salesman_department,
                operator_name,
                operator_department,
                coloader_name,
                job_handling_agent,
                bl_handling_agent,
                is_transhipment,
                pro2_system_id
            FROM (
                SELECT 
                    t1.job_date,
                    t1.job_type_cn,
                    t1.job_no,
                    t1.bkbl_no,
                    t1.client_name,
                    t1.vessel,
                    t1.voyage,
                    t1.job_pol,
                    t1.bill_pol,
                    t1.bill_pod,
                    t1.service_mode,
                    t1.lcl_rt,
                    t1.teu,
                    t1.air_weight,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.transhipment_profit,
                    t1.total_business_profit,
                    t1.is_freehand,
                    t1.salesman_name,
                    t1.salesman_department,
                    t1.operator_name,
                    t1.operator_department,
                    t1.coloader_name,
                    t1.job_handling_agent,
                    t1.bl_handling_agent,
                    t1.is_transhipment,
                    t1.pro2_system_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_booking_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
                AND t1.client_name = %s
        """
        
        params = [begin_date, end_date.strftime('%Y-%m-%d'), customer_name]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
        """
        
        print("执行高性能数据库查询...")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                all_records = await cursor.fetchall()
        
        print(f"查询完成，获得 {len(all_records)} 条记录")
        
        if not all_records:
            # 如果没有数据，返回空结果结构
            results = []
            current_date = start_date
            for month_offset in range(month_count):
                month_data = {
                    "year": f"{current_date.year}",
                    "month": f"{current_date.month:02d}",
                    "pro2_system_id": pro2_system_id,
                    "customer_name": customer_name,
                    "data": [{
                        "pod_code": "无数据",
                        "bkbl_count": 0,
                        "rt": 0,
                        "teu": 0,
                        "air_weight": 0,
                        "income": 0,
                        "cost": 0,
                        "profit": 0,
                        "profit_rate": 0,
                        "all_nominated_count": 0,
                        "all_nominated_rt": 0,
                        "all_nominated_teu": 0,
                        "all_nominated_profit": 0,
                        "nomi_agent_count": 0
                    }]
                }
                results.append(month_data)
                current_date = current_date + relativedelta(months=1)
            return results
        
        # 应用特例处理并按月份和目的港分组
        monthly_pod_data = defaultdict(lambda: defaultdict(list))
        
        for record in all_records:
            # 应用特例处理
            processed_record = apply_special_case_processing(record)
            
            job_date = processed_record['job_date']
            year_month = f"{job_date.year}-{job_date.month:02d}"
            
            # 获取卸货港，应用特例处理后的值
            pod_code = processed_record.get('bill_pod', '') or ''
            if not pod_code:
                pod_code = '未知港口'
            
            # 按月份和目的港分组数据
            monthly_pod_data[year_month][pod_code].append(processed_record)
        
        # 构建结果
        results = []
        current_date = start_date
        
        for month_offset in range(month_count):
            year_month = f"{current_date.year}-{current_date.month:02d}"
            
            # 获取该月的数据
            pod_data_dict = monthly_pod_data.get(year_month, {})
            
            month_data = {
                "year": f"{current_date.year}",
                "month": f"{current_date.month:02d}",
                "pro2_system_id": pro2_system_id,
                "customer_name": customer_name,
                "data": []
            }
            
            if pod_data_dict:
                # 计算各卸货港的汇总数据
                for pod_code, records in pod_data_dict.items():
                    aggregated_data = calculate_aggregated_data(records)
                    
                    # 构建目的港数据
                    pod_result = {
                        "pod_code": pod_code,
                        **aggregated_data
                    }
                    
                    # 移除不相关的字段（这些字段在客户维度分析中不适用）
                    pod_result.pop('customer_count', None)  # 客户维度分析不需要客户数量
                    
                    month_data["data"].append(pod_result)
                
                # 按卸货港代码排序
                month_data["data"].sort(key=lambda x: x["pod_code"])
            else:
                # 如果该月没有数据，添加空数据记录
                empty_data = calculate_aggregated_data([])
                empty_data.pop('customer_count', None)
                month_data["data"].append({
                    "pod_code": "无数据",
                    **empty_data
                })
            
            results.append(month_data)
            current_date = current_date + relativedelta(months=1)
        
        # 转换为DataFrame格式
        columns_info = {
            "year": "年份",
            "month": "月份",
            "pro2_system_id": "分公司代码",
            "customer_name": "客户名称",
            "pod_code": "卸货港代码",
            "bkbl_count": "票数",
            "rt": "计费吨",
            "teu": "TEU",
            "air_weight": "空运重量",
            "income": "收入",
            "cost": "成本",
            "profit": "利润",
            "profit_rate": "利润率(%)",
            "all_nominated_count": "指定货票数",
            "all_nominated_rt": "指定货RT",
            "all_nominated_profit": "指定货利润",
            "nomi_agent_count": "指定货代理数量"
        }
        df_data = convert_customer_analysis_to_dataframe(results, columns_info)
        
        return {
            "json_data": results,
            "pd_data": df_data,
            "metadata": {
                "data_type": "customer_analysis",
                "customer_name": customer_name,
                "analysis_period": f"{begin_date} to {end_date.strftime('%Y-%m-%d')}",
                "month_count": month_count,
                "pro2_system_id": pro2_system_id,
                "total_months": len(results),
                "columns_info": {
                    "year": "年份",
                    "month": "月份",
                    "pro2_system_id": "分公司代码",
                    "customer_name": "客户名称",
                    "pod_code": "卸货港代码",
                    "bkbl_count": "票数",
                    "rt": "计费吨",
                    "teu": "TEU",
                    "air_weight": "空运重量",
                    "income": "收入",
                    "cost": "成本",
                    "profit": "利润",
                    "profit_rate": "利润率(%)",
                    "all_nominated_count": "指定货票数",
                    "all_nominated_rt": "指定货RT",
                    "all_nominated_profit": "指定货利润",
                    "nomi_agent_count": "指定货代理数量"
                }
            }
        }
        
    except Exception as e:
        print(f"高性能分析客户数据时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "json_data": [],
            "pd_data": pd.DataFrame(),
            "metadata": {
                "data_type": "customer_analysis",
                "customer_name": customer_name,
                "error": str(e),
                "analysis_period": f"{begin_date} (error occurred)",
                "month_count": month_count,
                "pro2_system_id": pro2_system_id
            }
        }

# 常用分析场景5-1: 分析连续n个月利润前n大客户的数据(Booking分析） - 检查ok
async def analysis_booking_by_top_n_profit_customers(begin_date: str, month_count: int, top_n: int = 5, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    高性能优化版本: 分析连续n个月利润前n大客户的数据(Booking分析)
    
    主要优化:
    1. 使用窗口函数替代相关子查询，大幅提升SQL查询性能
    2. 一次性查询整个时间范围的数据，避免多次数据库查询
    3. 在内存中进行客户排序和数据聚合，减少数据库负载
    4. 优化数据结构和处理流程
    
    Args:
        begin_date: 开始日期，格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        top_n: 前n大客户数量，不允许超过10
        pro2_system_id: 分公司代码
    
    Returns:
        List[Dict], 包含前n名客户的月度分析数据
    """
    try:
        # 参数验证
        if top_n > 10:
            raise ValueError("top_n不允许超过10")
        
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"高性能查询前{top_n}大客户: {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 使用窗口函数一次性查询所有数据，排除指定货（is_freehand=0）
        sql = """
            SELECT 
                job_date,
                client_name,
                bill_pod,
                lcl_rt,
                teu,
                air_weight,
                income,
                cost,
                profit,
                is_freehand,
                salesman_name,
                salesman_department,
                job_handling_agent,
                pro2_system_id
            FROM (
                SELECT 
                    t1.job_date,
                    t1.client_name,
                    t1.bill_pod,
                    t1.lcl_rt,
                    t1.teu,
                    t1.air_weight,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.is_freehand,
                    t1.salesman_name,
                    t1.salesman_department,
                    t1.job_handling_agent,
                    t1.pro2_system_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_booking_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
                AND t1.is_freehand != 0
                AND t1.client_name IS NOT NULL 
                AND t1.client_name != ''
        """
        
        params = [begin_date, end_date.strftime('%Y-%m-%d')]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
        """
        
        print("执行高性能数据库查询...")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                all_records = await cursor.fetchall()
        
        print(f"查询完成，获得 {len(all_records)} 条记录")
        
        if not all_records:
            return []
        
        # 应用特例处理并按月份和客户分组
        monthly_customer_data = defaultdict(lambda: defaultdict(list))
        monthly_customer_profit = defaultdict(lambda: defaultdict(float))
        
        for record in all_records:
            # 应用特例处理
            processed_record = apply_special_case_processing(record)
            
            job_date = processed_record['job_date']
            year_month = f"{job_date.year}-{job_date.month:02d}"
            customer_name = processed_record['client_name'].strip()
            
            # 按月份和客户分组数据
            monthly_customer_data[year_month][customer_name].append(processed_record)
            
            # 累计客户在该月的利润
            profit = float(processed_record.get('profit', 0) or 0)
            monthly_customer_profit[year_month][customer_name] += profit
        
        # 收集所有月份的前n大客户
        all_top_customers = set()
        
        for year_month, customer_profits in monthly_customer_profit.items():
            # 按利润排序，取前n名
            sorted_customers = sorted(customer_profits.items(), key=lambda x: x[1], reverse=True)[:top_n]
            for customer_name, _ in sorted_customers:
                all_top_customers.add(customer_name)
        
        print(f"找到前{top_n}大客户共 {len(all_top_customers)} 个")
        
        if not all_top_customers:
            return []
        
        # 构建结果
        results = []
        
        for customer_name in sorted(all_top_customers):
            customer_result = {
                "customer_name": customer_name,
                "pro2_system_id": pro2_system_id,
                "data": []
            }
            
            # 为每个月生成数据
            current_date = start_date
            for month_offset in range(month_count):
                year_month = f"{current_date.year}-{current_date.month:02d}"
                
                # 获取该客户该月的数据
                customer_records = monthly_customer_data[year_month].get(customer_name, [])
                
                if customer_records:
                    # 计算目的港数量（去重）
                    pod_set = set()
                    for record in customer_records:
                        pod_code = record.get('bill_pod', '') or ''
                        if pod_code:
                            pod_set.add(pod_code)
                    
                    # 计算汇总数据
                    stats = _calculate_records_stats(customer_records)
                    
                    month_data = {
                        "year": f"{current_date.year}",
                        "month": f"{current_date.month:02d}",
                        "pod_count": len(pod_set),
                        "bkbl_count": stats["bkbl_count"],
                        "rt": stats["rt"],
                        "teu": stats["teu"],
                        "air_weight": stats["air_weight"],
                        "income": stats["income"],
                        "cost": stats["cost"],
                        "profit": stats["profit"],
                        "profit_rate": stats["profit_rate"],
                    }
                else:
                    # 如果该月没有数据，添加空数据
                    month_data = {
                        "year": f"{current_date.year}",
                        "month": f"{current_date.month:02d}",
                        "pod_count": 0,
                        "bkbl_count": 0,
                        "rt": 0,
                        "teu": 0,
                        "air_weight": 0,
                        "income": 0,
                        "cost": 0,
                        "profit": 0,
                        "profit_rate": 0,
                    }
                
                customer_result["data"].append(month_data)
                current_date = current_date + relativedelta(months=1)
            
            results.append(customer_result)
        
        # 转换为DataFrame格式
        columns_info = {
            "customer_name": "客户名称",
            "year": "年份",
            "month": "月份",
            "pro2_system_id": "分公司代码",
            "entity_type": "实体类型",
            "pod_count": "目的港数量",
            "bkbl_count": "票数",
            "rt": "计费吨",
            "teu": "TEU",
            "air_weight": "空运重量",
            "income": "收入",
            "cost": "成本",
            "profit": "利润",
            "profit_rate": "利润率(%)"
        }
        df_data = convert_entity_analysis_to_dataframe(results, 'customer', columns_info)
        
        return {
            "json_data": results,
            "pd_data": df_data,
            "metadata": {
                "data_type": "top_customers_analysis",
                "analysis_period": f"{begin_date} to {end_date.strftime('%Y-%m-%d')}",
                "month_count": month_count,
                "top_n": top_n,
                "pro2_system_id": pro2_system_id,
                "total_customers": len(results),
                "columns_info": {
                    "customer_name": "客户名称",
                    "year": "年份",
                    "month": "月份",
                    "pro2_system_id": "分公司代码",
                    "entity_type": "实体类型",
                    "pod_count": "目的港数量",
                    "bkbl_count": "票数",
                    "rt": "计费吨",
                    "teu": "TEU",
                    "air_weight": "空运重量",
                    "income": "收入",
                    "cost": "成本",
                    "profit": "利润",
                    "profit_rate": "利润率(%)"
                }
            }
        }
        
    except Exception as e:
        print(f"高性能分析前n大客户数据时发生错误: {str(e)}")
        traceback.print_exc()
        return {
            "json_data": [],
            "pd_data": pd.DataFrame(),
            "metadata": {
                "data_type": "top_customers_analysis",
                "error": str(e),
                "analysis_period": f"{begin_date} (error occurred)",
                "month_count": month_count,
                "top_n": top_n,
                "pro2_system_id": pro2_system_id
            }
        }

# 常用分析场景5-2: 分析连续n个月业务票数前n大客户的数据(Booking分析）- 检查ok
async def analysis_booking_by_top_n_bl_customers(begin_date: str, month_count: int, top_n: int = 5, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    高性能优化版本: 分析连续n个月业务票数前n大客户的数据(Booking分析)
    
    主要优化:
    1. 使用窗口函数替代相关子查询，大幅提升SQL查询性能
    2. 一次性查询整个时间范围的数据，避免多次数据库查询
    3. 在内存中进行客户排序和数据聚合，减少数据库负载
    4. 按票数（每个booking/bl作为一票）排序客户
    
    Args:
        begin_date: 开始日期，格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        top_n: 前n大客户数量，不允许超过10
        pro2_system_id: 分公司代码
    
    Returns:
        Dict[str, Any]: 包含前n名客户的月度分析数据（按票数排序）
    """
    try:
        # 参数验证
        if top_n > 10:
            raise ValueError("top_n不允许超过10")
        
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"高性能查询票数前{top_n}大客户: {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 使用窗口函数一次性查询所有数据，排除指定货（is_freehand=0）
        sql = """
            SELECT 
                job_date,
                job_type_cn,
                client_name,
                bill_pod,
                lcl_rt,
                teu,
                air_weight,
                income,
                cost,
                profit,
                is_freehand,
                salesman_name,
                salesman_department,
                job_handling_agent,
                pro2_system_id
            FROM (
                SELECT 
                    t1.job_date,
                    t1.job_type_cn,
                    t1.client_name,
                    t1.bill_pod,
                    t1.lcl_rt,
                    t1.teu,
                    t1.air_weight,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.is_freehand,
                    t1.salesman_name,
                    t1.salesman_department,
                    t1.job_handling_agent,
                    t1.pro2_system_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_booking_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
                AND t1.is_freehand != 0
                AND t1.client_name IS NOT NULL 
                AND t1.client_name != ''
        """
        
        params = [begin_date, end_date.strftime('%Y-%m-%d')]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
        """
        
        print("执行高性能数据库查询...")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                all_records = await cursor.fetchall()
        
        print(f"查询完成，获得 {len(all_records)} 条记录")
        
        if not all_records:
            return {
                "json_data": [],
                "pd_data": pd.DataFrame(),
                "metadata": {
                    "data_type": "top_bl_customers_analysis",
                    "analysis_period": f"{begin_date} to {end_date.strftime('%Y-%m-%d')}",
                    "month_count": month_count,
                    "top_n": top_n,
                    "pro2_system_id": pro2_system_id,
                    "total_customers": 0,
                    "message": "未查询到数据"
                }
            }
        
        # 应用特例处理并按月份和客户分组
        monthly_customer_data = defaultdict(lambda: defaultdict(list))
        monthly_customer_tickets = defaultdict(lambda: defaultdict(int))
        
        for record in all_records:
            # 应用特例处理
            processed_record = apply_special_case_processing(record)
            
            job_date = processed_record['job_date']
            year_month = f"{job_date.year}-{job_date.month:02d}"
            customer_name = processed_record['client_name'].strip()
            
            # 按月份和客户分组数据
            monthly_customer_data[year_month][customer_name].append(processed_record)
            
            # 累计客户在该月的票数（每条记录代表一票）
            monthly_customer_tickets[year_month][customer_name] += 1
        
        # 收集所有月份的前n大客户（按票数）
        all_top_customers = set()
        
        for year_month, customer_tickets in monthly_customer_tickets.items():
            # 按票数排序，取前n名
            sorted_customers = sorted(customer_tickets.items(), key=lambda x: x[1], reverse=True)[:top_n]
            for customer_name, _ in sorted_customers:
                all_top_customers.add(customer_name)
        
        print(f"找到票数前{top_n}大客户共 {len(all_top_customers)} 个")
        
        if not all_top_customers:
            return {
                "json_data": [],
                "pd_data": pd.DataFrame(),
                "metadata": {
                    "data_type": "top_bl_customers_analysis",
                    "analysis_period": f"{begin_date} to {end_date.strftime('%Y-%m-%d')}",
                    "month_count": month_count,
                    "top_n": top_n,
                    "pro2_system_id": pro2_system_id,
                    "total_customers": 0,
                    "message": "未找到符合条件的客户"
                }
            }
        
        # 构建结果
        results = []
        
        for customer_name in sorted(all_top_customers):
            customer_result = {
                "customer_name": customer_name,
                "pro2_system_id": pro2_system_id,
                "data": []
            }
            
            # 为每个月生成数据
            current_date = start_date
            for month_offset in range(month_count):
                year_month = f"{current_date.year}-{current_date.month:02d}"
                
                # 获取该客户该月的数据
                customer_records = monthly_customer_data[year_month].get(customer_name, [])
                
                if customer_records:
                    # 计算目的港数量（去重）
                    pod_set = set()
                    business_type_set = set()
                    for record in customer_records:
                        pod_code = record.get('bill_pod', '') or ''
                        if pod_code:
                            pod_set.add(pod_code)
                        business_type = record.get('job_type_cn', '') or ''
                        if business_type:
                            business_type_set.add(business_type)
                    
                    # 计算汇总数据
                    stats = _calculate_records_stats(customer_records)
                    
                    month_data = {
                        "year": f"{current_date.year}",
                        "month": f"{current_date.month:02d}",
                        "pod_count": len(pod_set),
                        "business_type_count": len(business_type_set),
                        "bkbl_count": stats["bkbl_count"],  # 这就是票数
                        "rt": stats["rt"],
                        "teu": stats["teu"],
                        "air_weight": stats["air_weight"],
                        "income": stats["income"],
                        "cost": stats["cost"],
                        "profit": stats["profit"],
                        "profit_rate": stats["profit_rate"],
                    }
                else:
                    # 如果该月没有数据，添加空数据
                    month_data = {
                        "year": f"{current_date.year}",
                        "month": f"{current_date.month:02d}",
                        "pod_count": 0,
                        "business_type_count": 0,
                        "bkbl_count": 0,
                        "rt": 0,
                        "teu": 0,
                        "air_weight": 0,
                        "income": 0,
                        "cost": 0,
                        "profit": 0,
                        "profit_rate": 0,
                    }
                
                customer_result["data"].append(month_data)
                current_date = current_date + relativedelta(months=1)
            
            results.append(customer_result)
        
        # 转换为DataFrame格式
        columns_info = {
            "customer_name": "客户名称",
            "year": "年份",
            "month": "月份",
            "pro2_system_id": "分公司代码",
            "entity_type": "实体类型",
            "pod_count": "目的港数量",
            "business_type_count": "业务类型数量",
            "bkbl_count": "票数",
            "rt": "计费吨",
            "teu": "TEU",
            "air_weight": "空运重量",
            "income": "收入",
            "cost": "成本",
            "profit": "利润",
            "profit_rate": "利润率(%)"
        }
        df_data = convert_entity_analysis_to_dataframe(results, 'customer', columns_info)
        
        return {
            "json_data": results,
            "pd_data": df_data,
            "metadata": {
                "data_type": "top_bl_customers_analysis",
                "analysis_period": f"{begin_date} to {end_date.strftime('%Y-%m-%d')}",
                "month_count": month_count,
                "top_n": top_n,
                "pro2_system_id": pro2_system_id,
                "total_customers": len(results),
                "ranking_criteria": "业务票数（每个booking/bl作为一票）",
                "columns_info": {
                    "customer_name": "客户名称",
                    "year": "年份",
                    "month": "月份",
                    "pro2_system_id": "分公司代码",
                    "entity_type": "实体类型",
                    "pod_count": "目的港数量",
                    "business_type_count": "业务类型数量",
                    "bkbl_count": "票数",
                    "rt": "计费吨",
                    "teu": "TEU",
                    "air_weight": "空运重量",
                    "income": "收入",
                    "cost": "成本",
                    "profit": "利润",
                    "profit_rate": "利润率(%)"
                }
            }
        }
        
    except Exception as e:
        print(f"高性能分析票数前n大客户数据时发生错误: {str(e)}")
        traceback.print_exc()
        return {
            "json_data": [],
            "pd_data": pd.DataFrame(),
            "metadata": {
                "data_type": "top_bl_customers_analysis",
                "error": str(e),
                "analysis_period": f"{begin_date} (error occurred)",
                "month_count": month_count,
                "top_n": top_n,
                "pro2_system_id": pro2_system_id
            }
        }

# 常用分析场景5-3: 分析连续n个月票数最多的n个指定货代理(Booking分析) - 检查ok
async def analysis_booking_nomi_agents(begin_date: str, month_count: int, top_n: int = 10, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    真正的高性能优化版本: 分析连续n个月票数最多的n个指定货代理(Booking分析)
    
    主要优化:
    1. 使用窗口函数替代相关子查询，大幅提升SQL查询性能
    2. 一次性查询整个时间范围的数据，避免多次数据库查询
    3. 在内存中进行代理排序和数据聚合，减少数据库负载
    4. 优化数据结构和处理流程
    
    Args:
        begin_date: 开始日期，格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        top_n: 前n大代理数量，默认10
        pro2_system_id: 分公司代码
    
    Returns:
        List[Dict], 包含前n名指定货代理的月度分析数据
    """
    try:
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"高性能查询指定货代理: {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 对于86021需要特殊处理，不能直接用is_freehand=0过滤
        if pro2_system_id == 86021:
            # 对于86021，需要查询所有记录然后在内存中应用特例规则
            sql = """
                SELECT 
                    job_date,
                    client_name,
                    lcl_rt,
                    teu,
                    air_weight,
                    income,
                    cost,
                    profit,
                    is_freehand,
                    salesman_name,
                    salesman_department,
                    job_handling_agent,
                    pro2_system_id
                FROM (
                    SELECT 
                        t1.job_date,
                        t1.client_name,
                        t1.lcl_rt,
                        t1.teu,
                        t1.air_weight,
                        t1.income,
                        t1.cost,
                        t1.profit,
                        t1.is_freehand,
                        t1.salesman_name,
                        t1.salesman_department,
                        t1.job_handling_agent,
                        t1.pro2_system_id,
                        ROW_NUMBER() OVER (
                            PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                            ORDER BY t1.id DESC
                        ) as rn
                    FROM t_booking_details t1
                    WHERE t1.job_date >= %s AND t1.job_date <= %s
                    AND t1.pro2_system_id = %s
                    AND (t1.is_freehand = 0 OR (t1.is_freehand = 1 AND t1.salesman_department = '指定货业务'))
            """
            params = [begin_date, end_date.strftime('%Y-%m-%d'), pro2_system_id]
        else:
            # 其他分公司正常查询指定货记录
            sql = """
                SELECT 
                    job_date,
                    client_name,
                    lcl_rt,
                    teu,
                    air_weight,
                    income,
                    cost,
                    profit,
                    is_freehand,
                    salesman_name,
                    salesman_department,
                    job_handling_agent,
                    pro2_system_id
                FROM (
                    SELECT 
                        t1.job_date,
                        t1.client_name,
                        t1.lcl_rt,
                        t1.teu,
                        t1.air_weight,
                        t1.income,
                        t1.cost,
                        t1.profit,
                        t1.is_freehand,
                        t1.salesman_name,
                        t1.salesman_department,
                        t1.job_handling_agent,
                        t1.pro2_system_id,
                        ROW_NUMBER() OVER (
                            PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                            ORDER BY t1.id DESC
                        ) as rn
                    FROM t_booking_details t1
                    WHERE t1.job_date >= %s AND t1.job_date <= %s
                    AND t1.is_freehand = 0
            """
            params = [begin_date, end_date.strftime('%Y-%m-%d')]
            
            # 添加分公司过滤条件
            if pro2_system_id is not None:
                sql += " AND t1.pro2_system_id = %s"
                params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
        """
        
        print("执行高性能数据库查询...")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                all_records = await cursor.fetchall()
        
        print(f"查询完成，获得 {len(all_records)} 条指定货记录")
        
        if not all_records:
            return []
        
        # 处理指定货代理名称并按月份和代理分组
        monthly_agent_data = defaultdict(lambda: defaultdict(list))
        monthly_agent_tickets = defaultdict(lambda: defaultdict(int))
        
        processed_count = 0
        for record in all_records:
            # 统一对所有分公司应用特例处理逻辑
            processed_record = apply_special_case_processing(record)
            
            # 只有经过特例处理后真正为指定货的记录才统计
            if processed_record.get('is_freehand') != 0:
                continue  # 跳过非指定货记录
            
            # 获取经过特例处理的指定货代理名称
            nomi_agent_name = processed_record.get('nomi_agent_name', '') or ''
            
            # 处理有效的指定货代理记录
            if nomi_agent_name.strip():
                agent_name = nomi_agent_name.strip()
                processed_record['nomi_agent_name'] = agent_name
                
                job_date = processed_record['job_date']
                year_month = f"{job_date.year}-{job_date.month:02d}"
                
                # 按月份和代理分组数据
                monthly_agent_data[year_month][agent_name].append(processed_record)
                
                # 累计代理在该月的票数
                monthly_agent_tickets[year_month][agent_name] += 1
                processed_count += 1
        
        print(f"处理后有效记录: {processed_count} 条")
        
        if processed_count == 0:
            return []
        
        # 收集所有月份的前n大代理
        all_top_agents = set()
        
        for year_month, agent_tickets in monthly_agent_tickets.items():
            # 按票数排序，取前n名
            sorted_agents = sorted(agent_tickets.items(), key=lambda x: x[1], reverse=True)[:top_n]
            for agent_name, _ in sorted_agents:
                all_top_agents.add(agent_name)
        
        print(f"找到前{top_n}大代理共 {len(all_top_agents)} 个")
        
        if not all_top_agents:
            return []
        
        # 构建结果
        results = []
        
        for agent_name in sorted(all_top_agents):
            agent_result = {
                "nomi_agent_name": agent_name,
                "pro2_system_id": pro2_system_id,
                "data": []
            }
            
            # 为每个月生成数据
            current_date = start_date
            for month_offset in range(month_count):
                year_month = f"{current_date.year}-{current_date.month:02d}"
                
                # 获取该代理该月的数据
                agent_records = monthly_agent_data[year_month].get(agent_name, [])
                
                if agent_records:
                    # 计算客户数量（去重）
                    customers = set()
                    for r in agent_records:
                        client_name = r.get('client_name', '')
                        if client_name and client_name.strip():
                            customers.add(client_name.strip())
                    
                    # 计算汇总数据
                    total_income = sum(float(r.get('income', 0) or 0) for r in agent_records)
                    total_cost = abs(sum(float(r.get('cost', 0) or 0) for r in agent_records))
                    total_profit = sum(float(r.get('profit', 0) or 0) for r in agent_records)
                    profit_rate = (total_profit / total_income * 100) if total_income > 0 else 0
                    
                    month_result = {
                        "year": f"{current_date.year}",
                        "month": f"{current_date.month:02d}",
                        "customer_count": len(customers),
                        "bkbl_count": len(agent_records),
                        "rt": sum(float(r.get('lcl_rt', 0) or 0) for r in agent_records),
                        "teu": sum(float(r.get('teu', 0) or 0) for r in agent_records),
                        "air_weight": sum(float(r.get('air_weight', 0) or 0) for r in agent_records),
                        "income": total_income,
                        "cost": total_cost,
                        "profit": total_profit,
                        "profit_rate": round(profit_rate, 2),
                    }
                else:
                    # 空数据
                    month_result = {
                        "year": f"{current_date.year}",
                        "month": f"{current_date.month:02d}",
                        "customer_count": 0,
                        "bkbl_count": 0,
                        "rt": 0,
                        "teu": 0,
                        "air_weight": 0,
                        "income": 0,
                        "cost": 0,
                        "profit": 0,
                        "profit_rate": 0,
                    }
                
                agent_result["data"].append(month_result)
                current_date = current_date + relativedelta(months=1)
            
            results.append(agent_result)
        
        # 转换为DataFrame格式
        columns_info = {
            "nomi_agent_name": "指定货代理名称",
            "year": "年份",
            "month": "月份",
            "pro2_system_id": "分公司代码",
            "entity_type": "实体类型",
            "customer_count": "客户数量",
            "bkbl_count": "票数",
            "rt": "计费吨",
            "teu": "TEU",
            "air_weight": "空运重量",
            "income": "收入",
            "cost": "成本",
            "profit": "利润",
            "profit_rate": "利润率(%)"
        }
        df_data = convert_entity_analysis_to_dataframe(results, 'agent', columns_info)
        
        return {
            "json_data": results,
            "pd_data": df_data,
            "metadata": {
                "data_type": "nomi_agents_analysis",
                "analysis_period": f"{begin_date} to {end_date.strftime('%Y-%m-%d')}",
                "month_count": month_count,
                "top_n": top_n,
                "pro2_system_id": pro2_system_id,
                "total_agents": len(results),
                "columns_info": {
                    "nomi_agent_name": "指定货代理名称",
                    "year": "年份",
                    "month": "月份",
                    "pro2_system_id": "分公司代码",
                    "entity_type": "实体类型",
                    "customer_count": "客户数量",
                    "bkbl_count": "票数",
                    "rt": "计费吨",
                    "teu": "TEU",
                    "air_weight": "空运重量",
                    "income": "收入",
                    "cost": "成本",
                    "profit": "利润",
                    "profit_rate": "利润率(%)"
                }
            }
        }
        
    except Exception as e:
        print(f"高性能分析指定货代理数据时发生错误: {str(e)}")
        traceback.print_exc()
        return {
            "json_data": [],
            "pd_data": pd.DataFrame(),
            "metadata": {
                "data_type": "nomi_agents_analysis",
                "error": str(e),
                "analysis_period": f"{begin_date} (error occurred)",
                "month_count": month_count,
                "top_n": top_n,
                "pro2_system_id": pro2_system_id
            }
        }

# 常用分析场景5-4: 分析连续n个月RT最高的n个目的港(Booking分析) - 检查ok
async def analysis_booking_top_n_rt(begin_date: str, month_count: int, top_n: int = 10, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    高性能优化版本: 分析连续n个月RT最高的n个目的港(Booking分析)
    
    主要优化:
    1. 使用窗口函数替代相关子查询，大幅提升SQL查询性能
    2. 减少内存中的数据处理步骤
    3. 优化数据结构和计算逻辑
    
    Args:
        begin_date: 开始日期，格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        top_n: 前n名目的港数量，默认10
        pro2_system_id: 分公司代码
    
    Returns:
        List[Dict], 包含前n名目的港的月度分析数据
    """
    try:
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"高性能查询前{top_n}名目的港RT: {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 使用窗口函数优化的SQL查询，避免相关子查询
        sql = """
            SELECT 
                job_date,
                bill_pod,
                client_name,
                lcl_rt,
                teu,
                air_weight,
                income,
                cost,
                profit,
                is_freehand,
                salesman_name,
                salesman_department,
                job_handling_agent,
                pro2_system_id
            FROM (
                SELECT 
                    t1.job_date,
                    t1.bill_pod,
                    t1.client_name,
                    t1.lcl_rt,
                    t1.teu,
                    t1.air_weight,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.is_freehand,
                    t1.salesman_name,
                    t1.salesman_department,
                    t1.job_handling_agent,
                    t1.pro2_system_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_booking_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
                AND t1.bill_pod IS NOT NULL 
                AND t1.bill_pod != ''
        """
        
        params = [begin_date, end_date.strftime('%Y-%m-%d')]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
        """
        
        print("执行优化的数据库查询...")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                all_records = await cursor.fetchall()
        
        print(f"查询完成，获得 {len(all_records)} 条记录")
        
        if not all_records:
            return []
        
        # 快速预处理数据并按目的港分组
        pod_monthly_data = defaultdict(lambda: defaultdict(lambda: {'non_nomi': [], 'nomi': []}))
        monthly_pod_rt = defaultdict(lambda: defaultdict(float))
        
        for record in all_records:
            # 应用特例处理
            processed_record = record.copy()
            
            # 处理86021的特殊情况
            if record.get('pro2_system_id') == 86021:
                salesman_dept = record.get('salesman_department', '')
                if record.get('is_freehand') == 1 and salesman_dept == '指定货业务':
                    processed_record['is_freehand'] = 0
            
            # 确保nomi_agent_name字段存在
            processed_record['nomi_agent_name'] = record.get('job_handling_agent', '') or ''
            
            job_date = processed_record['job_date']
            year_month = f"{job_date.year}-{job_date.month:02d}"
            pod_code = processed_record['bill_pod']
            
            # 按目的港和月份分组
            if processed_record.get('is_freehand') == 0:  # 指定货
                pod_monthly_data[pod_code][year_month]['nomi'].append(processed_record)
            else:  # 非指定货
                pod_monthly_data[pod_code][year_month]['non_nomi'].append(processed_record)
                # 同时计算RT用于排序
                rt = float(processed_record.get('lcl_rt', 0) or 0)
                monthly_pod_rt[year_month][pod_code] += rt
        
        # 找出前n大目的港（基于非指定货RT）
        all_top_pods = set()
        for year_month, pod_rt in monthly_pod_rt.items():
            sorted_pods = sorted(pod_rt.items(), key=lambda x: x[1], reverse=True)[:top_n]
            for pod_code, _ in sorted_pods:
                all_top_pods.add(pod_code)
        
        print(f"找到前{top_n}大目的港共 {len(all_top_pods)} 个: {sorted(all_top_pods)}")
        
        if not all_top_pods:
            return []
        
        # 构建结果
        results = []
        
        for pod_code in sorted(all_top_pods):
            pod_result = {
                "pod_code": pod_code,
                "pro2_system_id": pro2_system_id,
                "data": []
            }
            
            # 为每个月生成数据
            current_date = start_date
            for month_offset in range(month_count):
                year_month = f"{current_date.year}-{current_date.month:02d}"
                
                # 获取该目的港该月的数据
                non_nomi_records = pod_monthly_data[pod_code].get(year_month, {}).get('non_nomi', [])
                nomi_records = pod_monthly_data[pod_code].get(year_month, {}).get('nomi', [])
                
                # 计算非指定货数据（主要数据）
                main_data = _calculate_records_stats(non_nomi_records)
                
                # 计算指定货数据
                nomi_data = _calculate_nominated_stats(nomi_records)
                
                # 合并数据
                month_result = {
                    "year": f"{current_date.year}",
                    "month": f"{current_date.month:02d}",
                    **main_data,
                    **nomi_data
                }
                
                pod_result["data"].append(month_result)
                current_date = current_date + relativedelta(months=1)
            
            results.append(pod_result)
        
        # 转换为DataFrame格式
        columns_info = {
            "pod_code": "目的港代码",
            "year": "年份",
            "month": "月份",
            "pro2_system_id": "分公司代码",
            "entity_type": "实体类型",
            "customer_count": "客户数量",
            "bkbl_count": "票数",
            "rt": "计费吨",
            "teu": "TEU",
            "air_weight": "空运重量",
            "income": "收入",
            "cost": "成本",
            "profit": "利润",
            "profit_rate": "利润率(%)",
            "all_nominated_customer_count": "指定货客户数量",
            "all_nominated_count": "指定货票数",
            "all_nominated_rt": "指定货RT",
            "all_nominated_teu": "指定货TEU",
            "all_nominated_air_weight": "指定货空运重量",
            "all_nominated_income": "指定货收入",
            "all_nominated_cost": "指定货成本",
            "all_nominated_profit": "指定货利润",
            "all_nominated_profit_rate": "指定货利润率(%)"
        }
        df_data = convert_entity_analysis_to_dataframe(results, 'port', columns_info)
        
        return {
            "json_data": results,
            "pd_data": df_data,
            "metadata": {
                "data_type": "top_rt_ports_analysis",
                "analysis_period": f"{begin_date} to {end_date.strftime('%Y-%m-%d')}",
                "month_count": month_count,
                "top_n": top_n,
                "pro2_system_id": pro2_system_id,
                "total_ports": len(results),
                "columns_info": {
                    "pod_code": "目的港代码",
                    "year": "年份",
                    "month": "月份",
                    "pro2_system_id": "分公司代码",
                    "entity_type": "实体类型",
                    "customer_count": "客户数量",
                    "bkbl_count": "票数",
                    "rt": "计费吨",
                    "teu": "TEU",
                    "air_weight": "空运重量",
                    "income": "收入",
                    "cost": "成本",
                    "profit": "利润",
                    "profit_rate": "利润率(%)",
                    "all_nominated_customer_count": "指定货客户数量",
                    "all_nominated_count": "指定货票数",
                    "all_nominated_rt": "指定货RT",
                    "all_nominated_teu": "指定货TEU",
                    "all_nominated_air_weight": "指定货空运重量",
                    "all_nominated_income": "指定货收入",
                    "all_nominated_cost": "指定货成本",
                    "all_nominated_profit": "指定货利润",
                    "all_nominated_profit_rate": "指定货利润率(%)"
                }
            }
        }
        
    except Exception as e:
        print(f"高性能分析前n名目的港RT数据时发生错误: {str(e)}")
        traceback.print_exc()
        return {
            "json_data": [],
            "pd_data": pd.DataFrame(),
            "metadata": {
                "data_type": "top_rt_ports_analysis",
                "error": str(e),
                "analysis_period": f"{begin_date} (error occurred)",
                "month_count": month_count,
                "top_n": top_n,
                "pro2_system_id": pro2_system_id
            }
        }

#########################################################################
# 提取数据函数
#########################################################################

# 提取数据函数：带缓存的从tokens表提取booking数据
@async_cached_data_function
async def get_sea_air_profit_from_tokens_table_cached(begin_date: str, end_date: str, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    根据时间周期从tokens表查询海运空运损益并添加转运利润字段 (调度器使用)
    使用 get_booking_details_from_tokens_table 函数直接获取包含转运功能的业务明细数据
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD'
        end_date: 结束日期, 格式为'YYYY-MM-DD'
        pro2_system_id: 分公司代码, 可选值为: 86532-QDO(青岛), 86021-SHA(上海), 852-HKG(香港), 8103-TKY(东京), 如果为空则提取所有分公司
    """
    print(f"开始从tokens表查询海空损益数据（含转运）: {begin_date} 到 {end_date}, 分公司: {pro2_system_id or '全部'}")
    
    try:
        # 直接调用新的函数获取数据
        results = await get_booking_details_from_tokens_table(begin_date, end_date, pro2_system_id)
        
        print(f"从tokens表海空损益数据查询完成（含转运）: {results['total_count']} 条记录")
        
        # 检查results格式
        if not isinstance(results, dict) or 'data' not in results:
            print(f"错误：results格式不正确: {type(results)}")
            return {
                "json_data": {"data": [], "total_count": 0, "error": "数据格式错误"},
                "pd_data": pd.DataFrame(),
                "metadata": {
                    "data_type": "booking_details_extract",
                    "error": "数据格式错误",
                    "query_period": f"{begin_date} to {end_date}",
                    "pro2_system_id": pro2_system_id
                }
            }
        
        # 应用特例处理并添加nomi_agent_name字段
        processed_records = []
        print(f"开始处理 {len(results.get('data', []))} 条记录...")
        for record in results.get('data', []):
            try:
                # 使用统一的特例处理函数，确保86021等特例规则正确应用
                processed_record = apply_special_case_processing(record)
                
                # 确保nomi_agent_name字段存在（apply_special_case_processing已经处理了86021的情况）
                if 'nomi_agent_name' not in processed_record:
                    processed_record['nomi_agent_name'] = record.get('job_handling_agent', '') or ''
                
                # 对于数据提取场景，86021指定货需要额外处理：清空salesman字段
                if (record.get('pro2_system_id') == 86021 and 
                    processed_record.get('is_freehand') == 0 and 
                    record.get('salesman_department') == '指定货业务'):
                    # 对于从"指定货业务"部门转换来的指定货，清空salesman字段
                    processed_record['salesman_name'] = None
                    processed_record['salesman_department'] = None
                
                processed_records.append(processed_record)
            except Exception as field_error:
                print(f"处理记录时出错: {field_error}, 记录ID: {record.get('id', 'unknown')}")
                # 如果处理单条记录出错，添加原始记录并设置默认nomi_agent_name
                processed_record = record.copy()
                processed_record['nomi_agent_name'] = record.get('job_handling_agent', '') or ''
                processed_records.append(processed_record)
        
        print(f"记录处理完成，共处理 {len(processed_records)} 条记录")
        
        # 转换为DataFrame格式
        columns_info = {
            "job_type_cn": "业务类型",
            "job_date": "工作档日期",
            "job_no": "工作档编号",
            "bkbl_no": "订舱提单编号",
            "client_name": "客户名称",
            "vessel": "船名",
            "voyage": "航次",
            "job_pol": "工作档起运地",
            "bill_pol": "提单起运地",
            "bill_pod": "提单卸货地",
            "service_mode": "服务模式",
            "lcl_rt": "拼箱RT",
            "teu": "TEU",
            "air_weight": "空运重量",
            "income": "收入",
            "cost": "成本",
            "profit": "利润",
            "transhipment_profit": "转运利润",
            "total_business_profit": "总业务利润",
            "is_freehand": "是否自揽货",
            "salesman_name": "业务员",
            "salesman_department": "营业员部门",
            "nomi_agent_name": "指定货代理",
            "operator_name": "操作员",
            "operator_department": "操作部门",
            "coloader_name": "Coloader名称",
            "job_handling_agent": "工作档代理",
            "bl_handling_agent": "提单代理",
            "is_transhipment": "是否转运",
            "pro2_system_id": "分公司代码"
        }
        df_data = convert_records_to_dataframe(processed_records, columns_info)
        print(f"DataFrame转换完成，形状: {df_data.shape}")
        
        return {
            "json_data": results,
            "pd_data": df_data,
            "metadata": {
                "data_type": "booking_details_extract",
                "source_table": "mcp_tokens.t_booking_details",
                "query_period": f"{begin_date} to {end_date}",
                "pro2_system_id": pro2_system_id,
                "total_records": results.get('total_count', 0),
                "includes_transhipment": True,
                "columns_info": columns_info
            }
        }
        
    except Exception as e:
        print(f"从tokens表查询海空损益数据失败（含转运）: {e}")
        import traceback
        traceback.print_exc()
        error_result = {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部订舱毛利数据（含转运）- 从tokens表提取',
                'source_table': 'mcp_tokens.t_booking_details',
                'error': f'查询失败: {str(e)}'
            }
        }
        
        return {
            "json_data": error_result,
            "pd_data": pd.DataFrame(),
            "metadata": {
                "data_type": "booking_details_extract",
                "source_table": "mcp_tokens.t_booking_details",
                "error": str(e),
                "query_period": f"{begin_date} to {end_date}",
                "pro2_system_id": pro2_system_id
            }
        }

# 提取数据函数：带缓存的从tokens表提取job数据
@async_cached_data_function
async def get_job_details_from_tokens_table_cached(begin_date: str, end_date: str, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    根据时间周期从tokens表查询作业明细并添加转运利润字段 (调度器使用)
    使用 get_job_details_from_tokens_table 函数直接获取包含转运功能的作业明细数据
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD'
        end_date: 结束日期, 格式为'YYYY-MM-DD'
        pro2_system_id: 分公司代码, 可选值为: 86532-QDO(青岛), 86021-SHA(上海), 852-HKG(香港), 8103-TKY(东京), 如果为空则提取所有分公司
    """
    print(f"开始从tokens表查询作业明细数据（含转运）: {begin_date} 到 {end_date}, 分公司: {pro2_system_id or '全部'}")
    
    try:
        # 直接调用新的函数获取数据
        results = await get_job_details_from_tokens_table(begin_date, end_date, pro2_system_id)
        
        print(f"从tokens表作业明细数据查询完成（含转运）: {results['total_count']} 条记录")
        
        # 转换为DataFrame格式
        columns_info = {
            "job_type_cn": "业务类型",
            "job_date": "工作档日期",
            "job_no": "工作档编号",
            "vessel": "船名",
            "voyage": "航次",
            "pol_code": "起运港",
            "pod_code": "卸货港",
            "bk_count": "订舱数",
            "rt": "计费吨",
            "teu": "TEU",
            "income": "收入",
            "cost": "成本",
            "profit": "利润",
            "transhipment_profit": "转运利润",
            "total_business_profit": "总业务利润",
            "operator_name": "操作员",
            "job_handling_agent": "工作档代理",
            "nomi_count": "指定货票数",
            "nomi_rt": "指定货RT",
            "is_consolidation": "是否集拼",
            "bill_count": "提单数",
            "consolidation_20": "20集拼量",
            "consolidation_40": "40集拼量",
            "operator_department": "操作部门",
            "is_op_finished": "操作完成",
            "is_checked": "审核状态",
            "etd_date": "开船日期",
            "eta_date": "到港日期",
            "pro2_system_id": "分公司代码"
        }
        df_data = convert_records_to_dataframe(results.get('data', []), columns_info)
        
        return {
            "json_data": results,
            "pd_data": df_data,
            "metadata": {
                "data_type": "job_details_extract",
                "source_table": "mcp_tokens.t_job_details",
                "query_period": f"{begin_date} to {end_date}",
                "pro2_system_id": pro2_system_id,
                "total_records": results.get('total_count', 0),
                "includes_transhipment": True,
                "columns_info": columns_info
            }
        }
        
    except Exception as e:
        print(f"从tokens表查询作业明细数据失败（含转运）: {e}")
        import traceback
        traceback.print_exc()
        error_result = {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部作业明细数据（含转运）- 从tokens表提取',
                'source_table': 'mcp_tokens.t_job_details',
                'error': f'查询失败: {str(e)}'
            }
        }
        
        return {
            "json_data": error_result,
            "pd_data": pd.DataFrame(),
            "metadata": {
                "data_type": "job_details_extract",
                "source_table": "mcp_tokens.t_job_details",
                "error": str(e),
                "query_period": f"{begin_date} to {end_date}",
                "pro2_system_id": pro2_system_id
            }
        }
