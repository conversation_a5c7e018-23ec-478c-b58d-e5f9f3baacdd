#!/usr/bin/env python3
"""
利润数据周期性调度器
负责自动、周期性地运行 job_details 和 booking_details 数据分析并写入数据库
"""

import asyncio
import logging
import hashlib
import uuid
import os
from datetime import datetime, timedelta, date
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from decimal import Decimal
import asyncpg
import json

from utils.basic.logger_config import setup_logger
from utils.basic.pg_conn import get_postgres_connection_async, PG_DB_CMSDATA
from utils.database.db_pro2_basic import get_job_details_with_transhipment, get_sea_air_profit_with_transhipment
# 替换nan_value_cleaner为简单的内联函数
import pandas as pd

logger = setup_logger(
    name=__name__,
    level="warning",
    log_to_console=True,
    log_to_file=True
)

def is_nan_value(value) -> bool:
    """检查值是否为NaN"""
    if value is None:
        return False
    if pd.isna(value):
        return True
    if isinstance(value, str) and value.lower().strip() == 'nan':
        return True
    return False

def apply_basic_data_cleaning(row: Dict[str, Any]) -> Dict[str, Any]:
    """
    应用基本数据清理逻辑
    对数据进行基础清理，包括空值处理、类型转换等
    """
    cleaned_row = row.copy()
    
    # 处理常见的空值和异常值
    for key, value in cleaned_row.items():
        if value is None:
            continue
            
        # 处理字符串类型的值
        if isinstance(value, str):
            value = value.strip()
            
            # 处理特殊的无效日期值
            if value == '0000-00-00':
                cleaned_row[key] = None
            # 处理空值字符串
            elif value.lower() in ['', 'null', 'none', 'nan', 'n/a']:
                cleaned_row[key] = None
            # 处理日期字段
            elif 'date' in key.lower():
                try:
                    # 尝试解析日期字符串
                    cleaned_row[key] = pd.to_datetime(value).date()
                except:
                    cleaned_row[key] = None
            # 处理利润字段的特殊情况
            elif value == '0.00' and 'profit' in key.lower():
                cleaned_row[key] = 0.0
            else:
                cleaned_row[key] = value
        
        # 处理数值类型的NaN
        elif pd.isna(value):
            cleaned_row[key] = None
    
    return cleaned_row

@dataclass
class AnalysisPeriod:
    """分析周期配置"""
    start_date: date
    end_date: date
    period_type: str  # "2years+", "1-2years", "4months-1year", "current"
    check_frequency: str  # "3months", "1month", "1week"
    last_check: Optional[datetime] = None
    
class ProfitDataScheduler:
    """利润数据调度器"""
    
    def __init__(self):
        self.is_running = False
        self.current_task = None
        # 从环境变量获取系统ID
        self._validate_environment()
        self.pro2_system_id = self._get_dynamic_system_id()
        logger.info(f"当前系统ID: {self.pro2_system_id} (基于位置: {os.getenv('LOCATION', 'QD')})")

    def _get_dynamic_system_id(self) -> int:
        """
        根据LOCATION环境变量动态确定PRO2_SYSTEM_ID

        Returns:
            int: 对应位置的系统ID

        Raises:
            ValueError: 当LOCATION无效或对应的PRO2_SYSTEM_ID未配置时
        """
        location = os.getenv('LOCATION', 'QD').upper()
        system_id_key = f'PRO2_SYSTEM_ID_{location}'
        system_id_value = os.getenv(system_id_key)

        if not system_id_value:
            raise ValueError(f"未找到位置 {location} 对应的系统ID配置 {system_id_key}")

        try:
            system_id = int(system_id_value)
            if system_id <= 0:
                raise ValueError(f"位置 {location} 的系统ID必须是正整数，当前值: {system_id}")

            logger.info(f"动态确定系统ID: {location} -> {system_id}")
            return system_id

        except ValueError as e:
            if "invalid literal" in str(e):
                raise ValueError(f"位置 {location} 的系统ID配置无效: {system_id_value}")
            raise

    def _validate_environment(self):
        """验证必需的环境变量"""
        required_vars = {
            'PG_HOST': 'PostgreSQL服务器地址',
            'PG_USER': 'PostgreSQL用户名',
            'PG_PASSWORD': 'PostgreSQL密码',
            'PG_DB_CMSDATA': 'PostgreSQL数据库名'
        }

        missing_vars = []
        for var, description in required_vars.items():
            value = os.getenv(var)
            if not value:
                missing_vars.append(f"{var} ({description})")

        # 验证LOCATION和对应的系统ID配置
        location = os.getenv('LOCATION', 'QD').upper()
        system_id_key = f'PRO2_SYSTEM_ID_{location}'
        system_id_value = os.getenv(system_id_key)

        if not system_id_value:
            missing_vars.append(f"{system_id_key} (位置 {location} 的系统ID配置)")
        else:
            try:
                system_id = int(system_id_value)
                if system_id <= 0:
                    missing_vars.append(f"{system_id_key} 必须是正整数")
            except ValueError:
                missing_vars.append(f"{system_id_key} 必须是有效的整数")

        if missing_vars:
            error_msg = f"缺少必需的环境变量: {', '.join(missing_vars)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        logger.debug("环境变量验证通过")
        
    async def ensure_required_tables_exist(self):
        """跳过PostgreSQL表检查 - 根据要求不检查表是否存在和结构是否准确"""
        logger.info("跳过PostgreSQL表存在性和结构检查")
        return
        
    async def should_run_analysis(self, current_dt: datetime = None, period_type: str = None) -> bool:
        """
        检查是否应该运行分析 - 基于新的时间策略
        
        时间策略：
        - 距离当前时间1年及以上的业务：周六07:00至周日24:00连续执行检查
        - 距离当前时间6个月-12个月的业务：周六07:00至周日24:00连续执行检查
        - 距离当前时间3个月-5个月的业务：周六07:00至周日24:00连续执行检查
        - 距离当前时间1-2个月的业务：每日20:00-24:00执行检查
        
        特殊日期：
        - 2025年7月18日11:00-13:00：检查一年和以上的业务（还没有提取的业务），并进行提取（仅此一次）
        - 2025年8月19日15:00-24:00：检查所有数据（临时时间窗口）
        
        临时强制执行：
        - 如果设置了FORCE_IMMEDIATE_EXECUTION环境变量，立即执行所有分析
        """
        # 检查是否强制立即执行
        force_immediate = os.getenv('FORCE_IMMEDIATE_EXECUTION', '').lower() in ['true', '1', 'yes', 'on']
        if force_immediate:
            logger.info("⚡ 强制立即执行模式已启用，跳过时间窗口检查")
            return True
        
        if current_dt is None:
            current_dt = datetime.now()

        # 获取当前时间的各个组件
        current_hour = current_dt.hour
        current_weekday = current_dt.weekday()  # 0=Monday, 6=Sunday
        current_date = current_dt.date()
        
        # 特殊日期判断：2025年7月18日11:00-13:00执行一年及以上的业务数据提取（仅此一次）
        special_date = date(2025, 7, 18)
        if current_date == special_date:
            # 在特殊日期当日，检查一年和以上的业务在11:00-13:00执行
            is_in_special_time = 11 <= current_hour < 13  # 11:00-12:59
            if is_in_special_time and period_type == "1year+":
                logger.info(f"特殊日期 {special_date} 当日，11:00-13:00执行一年及以上的业务数据提取")
            return is_in_special_time and period_type == "1year+"
        
        # 临时时间窗口：2025年8月19日15:00-24:00检查所有数据
        temp_special_date = date(2025, 8, 19)
        if current_date == temp_special_date:
            # 在临时特殊日期当日，15:00-24:00执行所有类型的数据检查
            is_in_temp_special_time = current_hour >= 15  # 15:00-23:59
            if is_in_temp_special_time:
                logger.info(f"临时时间窗口 {temp_special_date} 当日，15:00-24:00执行所有数据检查")
            return is_in_temp_special_time
        
        # 以后按原计划执行：根据周期类型确定执行时间窗口
        if period_type in ["1year+", "6months-1year", "3months-6months"]:
            # 周六07:00至周日24:00连续执行
            if current_weekday == 5:  # 周六=5
                return current_hour >= 7  # 周六07:00-23:59
            elif current_weekday == 6:  # 周日=6
                return True  # 周日全天00:00-23:59
            else:
                return False
        elif period_type in ["1-2months"]:
            # 每日20:00-24:00
            return current_hour >= 20  # 20:00-23:59
        else:
            # 默认情况（向后兼容）
            return True

    def _get_month_start(self, year: int, month: int) -> date:
        """获取指定年月的第一天"""
        return date(year, month, 1)

    def _get_month_end(self, year: int, month: int) -> date:
        """获取指定年月的最后一天"""
        if month == 12:
            next_month_start = date(year + 1, 1, 1)
        else:
            next_month_start = date(year, month + 1, 1)
        return next_month_start - timedelta(days=1)

    def _add_months(self, start_date: date, months: int) -> date:
        """给日期添加指定月数，返回月份的第一天"""
        year = start_date.year
        month = start_date.month + months

        while month > 12:
            year += 1
            month -= 12
        while month < 1:
            year -= 1
            month += 12

        return self._get_month_start(year, month)

    def _get_quarter_info(self, date_obj: date) -> tuple:
        """获取日期所属的季度信息 (year, quarter)"""
        quarter = (date_obj.month - 1) // 3 + 1
        return date_obj.year, quarter

    def _get_quarter_start(self, year: int, quarter: int) -> date:
        """获取指定年份季度的开始日期"""
        month = (quarter - 1) * 3 + 1
        return date(year, month, 1)

    def _get_quarter_end(self, year: int, quarter: int) -> date:
        """获取指定年份季度的结束日期"""
        month = quarter * 3
        return self._get_month_end(year, month)

    def _add_quarters(self, year: int, quarter: int, quarters: int) -> tuple:
        """给季度添加指定数量的季度，返回(year, quarter)"""
        total_quarters = (year - 1) * 4 + quarter + quarters
        new_year = (total_quarters - 1) // 4 + 1
        new_quarter = (total_quarters - 1) % 4 + 1
        return new_year, new_quarter

    def get_analysis_periods(self, reference_date: date = None) -> List[AnalysisPeriod]:
        """
        获取需要分析的时间周期 - 按月份划分，实现新的时间策略
        
        新的时间策略：
        - 距离当前时间1年及以上的业务：每3个月检查一次，周六07:00至周日24:00连续执行
        - 距离当前时间6个月-12个月的业务：每1个月检查一次，周六07:00至周日24:00连续执行
        - 距离当前时间3个月-5个月的业务：每周检查一次，周六07:00至周日24:00连续执行
        - 距离当前时间1-2个月的业务：每日检查一次，每日20:00-24:00执行
        - 当前日期为1-7日时：免除检查上一个月的数据
        """
        if reference_date is None:
            reference_date = date.today()

        periods = []
        
        # 确定截止月份
        current_year = reference_date.year
        current_month = reference_date.month
        
        if reference_date.day <= 7:
            # 1-7日：分析到上上个月，免除检查上个月数据
            if current_month <= 2:
                end_year = current_year - 1
                end_month = current_month + 10  # 上上个月
            else:
                end_year = current_year
                end_month = current_month - 2
            logger.info(f"当前日期为{reference_date.day}日，免除检查上个月数据")
        else:
            # 8日及以后：分析到上个月
            if current_month == 1:
                end_year = current_year - 1
                end_month = 12
            else:
                end_year = current_year
                end_month = current_month - 1
        
        logger.info(f"数据分析时间范围：2020年1月 至 {end_year}年{end_month}月")
        
        # 生成从2020年1月到截止月份的所有月份
        start_year, start_month = 2020, 1
        
        year, month = start_year, start_month
        while (year, month) <= (end_year, end_month):
            # 计算月份开始和结束日期
            month_start = date(year, month, 1)
            if month == 12:
                month_end = date(year + 1, 1, 1) - timedelta(days=1)
            else:
                month_end = date(year, month + 1, 1) - timedelta(days=1)
            
            # 根据新的时间策略确定检查频率和周期类型
            months_ago = (current_year - year) * 12 + (current_month - month)
            
            if months_ago >= 12:  # 1年及以上
                period_type = "1year+"
                check_frequency = "3months"
            elif months_ago >= 6:  # 6个月-12个月
                period_type = "6months-1year"
                check_frequency = "1month"
            elif months_ago >= 3:  # 3个月-5个月
                period_type = "3months-6months"
                check_frequency = "1week"
            else:  # 1-2个月
                period_type = "1-2months"
                check_frequency = "1day"
            
            periods.append(AnalysisPeriod(
                start_date=month_start,
                end_date=month_end,
                period_type=period_type,
                check_frequency=check_frequency
            ))
            
            # 移动到下一个月
            if month == 12:
                year, month = year + 1, 1
            else:
                month += 1
        
        logger.info(f"生成了 {len(periods)} 个月度分析周期")
        return periods

    def _overlaps_excluded_months(self, start_date: date, end_date: date, excluded_months: List[date]) -> bool:
        """检查时间段是否与排除的月份重叠"""
        for excluded_month in excluded_months:
            # 获取排除月份的开始和结束日期
            month_start = excluded_month
            if excluded_month.month == 12:
                month_end = excluded_month.replace(year=excluded_month.year + 1, month=1) - timedelta(days=1)
            else:
                month_end = excluded_month.replace(month=excluded_month.month + 1) - timedelta(days=1)
                
            # 检查是否有重叠
            if not (end_date < month_start or start_date > month_end):
                return True
        return False
        
    async def get_last_check_time(self, period: AnalysisPeriod) -> Optional[datetime]:
        """从数据库获取周期的最后检查时间 - 修复版本，不依赖period_type避免重复检查"""
        try:
            async with get_postgres_connection_async(database=PG_DB_CMSDATA) as connection:
                result = await connection.fetchrow("""
                    SELECT last_check_time FROM t_scheduler_check_log
                    WHERE pro2_system_id = $1
                    AND period_start = $2
                    AND period_end = $3
                    ORDER BY last_check_time DESC
                    LIMIT 1
                """, self.pro2_system_id, period.start_date, period.end_date)
                # 移除 period_type 条件，因为同一周期的period_type可能随时间推移而变化
                # 这样确保能正确获取最后检查时间，避免因period_type变化导致的重复检查

                return result['last_check_time'] if result else None

        except Exception as e:
            logger.error(f"获取最后检查时间失败: {e}")
            return None
    
    async def update_last_check_time(self, period: AnalysisPeriod, check_time: datetime, job_hash: str = None, booking_hash: str = None):
        """更新周期的最后检查时间和数据哈希值到数据库"""
        try:
            async with get_postgres_connection_async(database=PG_DB_CMSDATA) as connection:
                await connection.execute("""
                    INSERT INTO t_scheduler_check_log
                    (pro2_system_id, period_start, period_end, period_type, check_frequency,
                     last_check_time, job_data_hash, booking_data_hash, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)
                    ON CONFLICT (pro2_system_id, period_start, period_end, period_type)
                    DO UPDATE SET
                        last_check_time = EXCLUDED.last_check_time,
                        job_data_hash = EXCLUDED.job_data_hash,
                        booking_data_hash = EXCLUDED.booking_data_hash,
                        updated_at = CURRENT_TIMESTAMP
                """, self.pro2_system_id, period.start_date, period.end_date,
                     period.period_type, period.check_frequency, check_time, job_hash, booking_hash)

        except Exception as e:
            logger.error(f"更新最后检查时间失败: {e}")
            
    async def needs_check(self, period: AnalysisPeriod, current_dt: datetime) -> bool:
        """
        检查周期是否需要重新检查
        
        特殊逻辑：
        - 周六时，如果距离上次检查相差一天（满足检查频率-1天），也提前执行
        - 这样可以防止周六被空闲，而周日运行时间不足的情况
        """
        # 检查是否强制立即执行
        force_immediate = os.getenv('FORCE_IMMEDIATE_EXECUTION', '').lower() in ['true', '1', 'yes', 'on']
        if force_immediate:
            logger.info(f"⚡ 强制立即执行模式：周期 {period.start_date}~{period.end_date} 需要检查")
            return True
        
        last_check = await self.get_last_check_time(period)
        if last_check is None:
            return True

        time_diff = current_dt - last_check
        current_weekday = current_dt.weekday()  # 0=Monday, 6=Sunday

        if period.check_frequency == "3months":
            # 周六时，如果相差一天也提前执行（原计划周日进行的提取）
            if current_weekday == 5 and time_diff.days >= 89:  # 周六=5，提前1天
                return True
            return time_diff.days >= 90
        elif period.check_frequency == "1month":
            # 周六时，如果相差一天也提前执行（原计划周日进行的提取）
            if current_weekday == 5 and time_diff.days >= 29:  # 周六=5，提前1天
                return True
            return time_diff.days >= 30
        elif period.check_frequency == "1week":
            # 周六时，如果相差一天也提前执行（原计划周日进行的提取）
            if current_weekday == 5 and time_diff.days >= 6:  # 周六=5，提前1天
                return True
            return time_diff.days >= 7
        elif period.check_frequency == "1day":
            # 修复：检查是否是不同的日期，而不是简单的days >= 1
            # 这样可以避免同一天内重复执行
            last_check_date = last_check.date()
            current_date = current_dt.date()
            return current_date > last_check_date

        return False
        
    async def get_session_id(self) -> str:
        """生成唯一的会话ID（限制在32字符内）"""
        timestamp = int(datetime.now().timestamp())
        random_str = str(uuid.uuid4())[:8]
        # 格式: p{timestamp}{random} - 确保在32字符内
        return f"p{timestamp}{random_str}"
    
    def clean_data_for_postgres(self, value):
        """清理数据中的NaN值，防止PostgreSQL错误"""
        if is_nan_value(value):
            return None
        return value
    
    def safe_int_convert(self, value, default=None):
        """安全的整数转换，处理None、空字符串、NaN、超大数值等情况"""
        if value is None or value == '' or is_nan_value(value):
            return default
        try:
            # 尝试转换为int
            if isinstance(value, str):
                value = value.strip()
                if value == '':
                    return default
            
            # 直接转换为int，避免float精度丢失
            try:
                int_val = int(value)
            except ValueError:
                # 如果直接转换失败，可能是小数字符串，再尝试先转float
                try:
                    int_val = int(float(value))
                except (ValueError, OverflowError):
                    logger.warning(f"无法转换为整数，使用默认值: {value}")
                    return default
            
            # 检查是否在bigint范围内 (-2^63 to 2^63-1)
            if int_val < -9223372036854775808 or int_val > 9223372036854775807:
                logger.warning(f"数值超出bigint范围，使用默认值: {value}")
                return default
                
            return int_val
        except (ValueError, TypeError, OverflowError):
            logger.warning(f"无法转换为整数，使用默认值: {value}")
            return default
    
    def _extract_nomi_agent_name_with_priority(self, processed_row: Dict[str, Any], is_freehand: int, original_row: Dict[str, Any] = None) -> str:
        """
        根据业务规则和优先级提取指定货代理名称

        Args:
            processed_row: 经过基本清理的数据行
            is_freehand: 是否自揽货标识
            original_row: 原始数据行（用于86021特殊处理）

        Returns:
            str: 指定货代理名称
        """
        # 只有在 is_freehand=0 时才需要设置 nomi_agent_name
        if is_freehand != 0:
            return ''

        # 对于86021系统，需要进行特殊的业务逻辑处理
        if self.pro2_system_id == 86021:
            if original_row is None:
                original_row = processed_row

            # 检查是否是从"指定货业务"部门转换来的指定货
            salesman_dept_name = original_row.get('salesman_dept_name', '') or original_row.get('salesman_department', '')
            salesman_name = original_row.get('salesman_name', '')

            if salesman_dept_name == '指定货业务' and salesman_name:
                # 从salesman_name中提取代理名称
                if '指定货' in salesman_name:
                    return salesman_name.replace('指定货', '').strip()
                elif '指定' in salesman_name:
                    return salesman_name.replace('指定', '').strip()
                else:
                    return salesman_name.strip()
            else:
                # 对于不是来自"指定货业务"部门的86021指定货，使用job_handling_agent
                return processed_row.get('job_handling_agent', '') or ''

        # 对于其他系统，按照优先级顺序提取：
        # 1. nomi_agent_name (如果已存在)
        # 2. bl_handling_agent (操作代理)
        # 3. job_handling_agent (工作档代理)

        # 优先级1: 原始数据中的 nomi_agent_name
        nomi_agent_name = processed_row.get('nomi_agent_name', '') or ''
        if nomi_agent_name.strip():
            return nomi_agent_name.strip()

        # 优先级2: bl_handling_agent（操作代理）
        bl_handling_agent = processed_row.get('bl_handling_agent', '') or ''
        if bl_handling_agent.strip():
            return bl_handling_agent.strip()

        # 优先级3: job_handling_agent（工作档代理）
        job_handling_agent = processed_row.get('job_handling_agent', '') or ''
        if job_handling_agent.strip():
            return job_handling_agent.strip()

        # 都没有，返回空字符串
        return ''
         
    async def calculate_record_hash(self, row: Dict[str, Any], data_type: str) -> str:
        """计算单条记录的哈希值，用于变更检测"""
        # 定义核心业务字段用于哈希计算（包含所有重要业务数据，只排除系统管理字段）
        if data_type == "job":
            key_fields = [
                'job_date', 'job_file_no', 'income', 'cost', 'profit',
                'vessel', 'voyage', 'pol_code', 'pod_code', 'total_teu', 'total_rt',
                'business_type_name', 'bk_count', 'operator_name', 'operator_department', 
                'job_handling_agent', 'nomi_count', 'nomi_rt', 'is_consolidation',
                'bill_count', 'consolidation_20', 'consolidation_40', 'is_op_finished', 'is_checked'
                # 只排除系统管理字段：id, created_at, updated_at, last_check_time, session_id, analysis_timestamp
            ]
        else:  # booking
            key_fields = [
                'job_date', 'job_file_no', 'business_no', 'income', 'cost', 'profit',
                'vessel', 'voyage', 'pol_code', 'pod_code', 'service_mode',
                'business_type_name', 'shipper_name', 'salesman_name', 'salesman_department',
                'operator_name', 'operator_department', 'coloader_name', 'job_handling_agent',
                'bl_handling_agent', 'is_freehand', 'is_transhipment', 'transhipment_profit',
                'total_business_profit', 'lcl_rt', 'teu', 'air_weight', 'nomi_agent_name'
                # 只排除系统管理字段：id, created_at, updated_at, last_check_time, session_id, analysis_timestamp
            ]
        
        # 构建标准化的单条记录用于哈希
        normalized_row = {}
        for field in key_fields:
            # 获取字段值，支持中文和英文字段名
            value = row.get(field)
            if value is None:
                # 尝试中文字段名映射
                cn_mapping = {
                    'job_date': '工作档日期',
                    'job_file_no': '工作档号',
                    'job_no': '工作档号', 
                    'income': '收入',
                    'cost': '成本',
                    'profit': '毛利润',
                    'operator_name': '操作',
                    'operator_department': '操作部门',
                    'vessel': '船名',
                    'voyage': '航次',
                    'pol_code': '起运地',
                    'pod_code': '卸货地',
                    'business_type_name': '业务类型中文',
                    'job_type_cn': '业务类型中文',
                    'salesman_name': '业务员',
                    'salesman_department': '业务员部门',
                    'bill_pol': '提单起运地',
                    'bill_pod': '提单卸货地',
                    'service_mode': '服务模式',
                    'client_name': '客户',
                    'shipper_name': '客户',
                    'business_no': '订舱/提单号',
                    'bkbl_no': '订舱/提单号',
                    'job_handling_agent': '工作档代理',
                    'bl_handling_agent': '操作代理',
                    'coloader_name': 'Coloader',
                    'bk_count': '订舱数',
                    'total_teu': 'TEU总计',
                    'total_rt': 'RT总计',
                    'nomi_count': '指定货票数',
                    'nomi_rt': '指定货RT总和',
                    'bill_count': '提单数量',
                    'consolidation_20': '集拼20尺柜数量',
                    'consolidation_40': '集拼40尺柜数量',
                    'transhipment_profit': '转运利润',
                    'total_business_profit': '业务总利润',
                    'lcl_rt': '拼箱计费重量',
                    'air_weight': '空运计费重量',
                    'nomi_agent_name': '指定货代理'
                }
                value = row.get(cn_mapping.get(field, field))
            
            # 标准化值
            if value is None:
                normalized_row[field] = ""
            elif isinstance(value, (int, float, Decimal)):
                normalized_row[field] = str(value)
            else:
                normalized_row[field] = str(value).strip()
        
        # 计算哈希
        data_str = json.dumps(normalized_row, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(data_str.encode('utf-8')).hexdigest()

    async def calculate_data_hash(self, data: List[Dict[str, Any]], data_type: str) -> str:
        """计算数据的哈希值，用于变更检测，只使用核心业务字段"""
        if not data:
            return "empty_data"

        # 定义核心业务字段用于哈希计算（包含所有重要业务数据，只排除系统管理字段）
        if data_type == "job":
            key_fields = [
                'job_date', 'job_file_no', 'income', 'cost', 'profit',
                'vessel', 'voyage', 'pol_code', 'pod_code', 'total_teu', 'total_rt',
                'business_type_name', 'bk_count', 'operator_name', 'operator_department', 
                'job_handling_agent', 'nomi_count', 'nomi_rt', 'is_consolidation',
                'bill_count', 'consolidation_20', 'consolidation_40', 'is_op_finished', 'is_checked'
                # 只排除系统管理字段：id, created_at, updated_at, last_check_time, session_id, analysis_timestamp
            ]
        else:  # booking
            key_fields = [
                'job_date', 'job_file_no', 'business_no', 'income', 'cost', 'profit',
                'vessel', 'voyage', 'pol_code', 'pod_code', 'service_mode',
                'business_type_name', 'shipper_name', 'salesman_name', 'salesman_department',
                'operator_name', 'operator_department', 'coloader_name', 'job_handling_agent',
                'bl_handling_agent', 'is_freehand', 'is_transhipment', 'transhipment_profit',
                'total_business_profit', 'lcl_rt', 'teu', 'air_weight', 'nomi_agent_name'
                # 只排除系统管理字段：id, created_at, updated_at, last_check_time, session_id, analysis_timestamp
            ]
        
        # 构建标准化的数据用于哈希
        normalized_data = []
        for row in data:
            normalized_row = {}
            for field in key_fields:
                # 获取字段值，支持中文和英文字段名
                value = row.get(field)
                if value is None:
                    # 尝试中文字段名映射
                    cn_mapping = {
                        'job_date': '工作档日期',
                        'job_file_no': '工作档号',
                        'job_no': '工作档号', 
                        'income': '收入',
                        'cost': '成本',
                        'profit': '毛利润',
                        'operator_name': '操作',
                        'operator_department': '操作部门',
                        'vessel': '船名',
                        'voyage': '航次',
                        'pol_code': '起运地',
                        'pod_code': '卸货地',
                        'business_type_name': '业务类型中文',
                        'job_type_cn': '业务类型中文',
                        'salesman_name': '业务员',
                        'salesman_department': '业务员部门',
                        'bill_pol': '提单起运地',
                        'bill_pod': '提单卸货地',
                        'service_mode': '服务模式',
                        'client_name': '客户',
                        'shipper_name': '客户',
                        'business_no': '订舱/提单号',
                        'bkbl_no': '订舱/提单号',
                        'job_handling_agent': '工作档代理',
                        'bl_handling_agent': '操作代理',
                        'coloader_name': 'Coloader',
                        'bk_count': '订舱数',
                        'total_teu': 'TEU总计',
                        'total_rt': 'RT总计',
                        'nomi_count': '指定货票数',
                        'nomi_rt': '指定货RT总和',
                        'bill_count': '提单数量',
                        'consolidation_20': '集拼20尺柜数量',
                        'consolidation_40': '集拼40尺柜数量',
                        'transhipment_profit': '转运利润',
                        'total_business_profit': '业务总利润',
                        'lcl_rt': '拼箱计费重量',
                        'air_weight': '空运计费重量',
                        'nomi_agent_name': '指定货代理'
                    }
                    value = row.get(cn_mapping.get(field, field))
                
                # 标准化值
                if value is None:
                    normalized_row[field] = ""
                elif isinstance(value, (int, float, Decimal)):
                    normalized_row[field] = str(value)
                else:
                    normalized_row[field] = str(value).strip()
            
            normalized_data.append(normalized_row)
        
        # 按业务关键字段排序确保一致性
        sort_key = 'job_date' if 'job_date' in key_fields else list(key_fields)[0]
        normalized_data.sort(key=lambda x: (x.get(sort_key, ''), x.get('job_no', '')))
        
        # 计算哈希
        data_str = json.dumps(normalized_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(data_str.encode('utf-8')).hexdigest()
        
    async def get_existing_data_hash(self, period: AnalysisPeriod, data_type: str) -> Optional[str]:
        """从检查日志表获取上次分析的数据哈希值 - 修复版本，不依赖period_type"""
        hash_field = "job_data_hash" if data_type == "job" else "booking_data_hash"

        try:
            async with get_postgres_connection_async(database=PG_DB_CMSDATA) as connection:
                result = await connection.fetchrow(f"""
                    SELECT {hash_field}
                    FROM t_scheduler_check_log
                    WHERE pro2_system_id = $1
                    AND period_start = $2
                    AND period_end = $3
                    AND {hash_field} IS NOT NULL
                    ORDER BY last_check_time DESC
                    LIMIT 1
                """, self.pro2_system_id, period.start_date, period.end_date)
                # 移除 period_type 条件，确保能获取到最新的哈希值进行比较

                return result[hash_field] if result else None

        except Exception as e:
            logger.error(f"获取现有数据哈希失败: {e}")
            return None
    
    async def get_changed_records(self, new_data: List[Dict[str, Any]], data_type: str, period: AnalysisPeriod) -> List[Dict[str, Any]]:
        """获取有变化的记录（基于记录级哈希比较）"""
        if not new_data:
            return []

        table_name = "t_job_details" if data_type == "job" else "t_booking_details"
        changed_records = []

        try:
            async with get_postgres_connection_async(database=PG_DB_CMSDATA) as connection:
                for row in new_data:
                    # 计算新记录的哈希值 - 使用单条记录计算哈希
                    new_hash = await self.calculate_record_hash(row, data_type)

                    # 通过job_no查找现有记录的最新哈希值
                    # job数据使用job_file_no字段，booking数据使用job_file_no或job_no字段
                    if data_type == "job":
                        job_no = row.get('job_file_no') or row.get('工作档号')
                    else:
                        job_no = row.get('job_file_no') or row.get('job_no') or row.get('工作档号')
                    if not job_no:
                        # 如果没有job_no，直接加入变化列表
                        changed_records.append(row)
                        continue

                    result = await connection.fetchrow(f"""
                        SELECT data_hash
                        FROM {table_name}
                        WHERE job_no = $1
                        AND pro2_system_id = $2
                        AND job_date BETWEEN $3 AND $4
                        ORDER BY analysis_timestamp DESC
                        LIMIT 1
                    """, job_no, self.pro2_system_id, period.start_date, period.end_date)

                    existing_hash = result['data_hash'] if result else None

                    # 如果哈希值不同或不存在，则认为是变化的记录
                    if existing_hash != new_hash:
                        changed_records.append(row)
                        logger.info(f"检测到变化记录 {job_no}: {existing_hash} -> {new_hash}")
                    else:
                        logger.debug(f"记录无变化 {job_no}: {existing_hash}")

        except Exception as e:
            logger.error(f"检查记录变化失败: {e}")
            # 发生错误时返回所有记录以确保数据不丢失
            return new_data

        logger.info(f"{data_type}数据：总记录 {len(new_data)} 条，变化记录 {len(changed_records)} 条")
        return changed_records
                
    async def save_job_details(self, data: List[Dict[str, Any]], session_id: str, analysis_timestamp: int):
        """保存Job详情数据到t_job_details表"""
        if not data:
            logger.warning("Job数据为空，跳过保存")
            return

        async with get_postgres_connection_async(database=PG_DB_CMSDATA) as connection:
            insert_sql = """
            INSERT INTO t_job_details (
                session_id, analysis_timestamp, created_at, pro2_system_id,
                job_type_cn, job_date, job_no, vessel, voyage, pol_code, pod_code,
                bk_count, rt, teu, income, cost, profit,
                transhipment_profit, total_business_profit,
                operator_name, job_handling_agent, nomi_count, nomi_rt,
                is_consolidation, bill_count, consolidation_20, consolidation_40,
                operator_department, is_op_finished, is_checked,
                job_id, job_type_id, operator_id, etd_date, eta_date, data_hash
            ) VALUES (
                $1, $2, CURRENT_TIMESTAMP, $3,
                $4, $5, $6, $7, $8, $9, $10,
                $11, $12, $13, $14, $15, $16,
                $17, $18,
                $19, $20, $21, $22,
                $23, $24, $25, $26,
                $27, $28, $29,
                $30, $31, $32, $33, $34, $35
            ) ON CONFLICT (data_hash) DO UPDATE SET
                session_id = EXCLUDED.session_id,
                analysis_timestamp = EXCLUDED.analysis_timestamp,
                created_at = CURRENT_TIMESTAMP
            """

            for row in data:
                # 计算单条记录的哈希值
                row_hash = await self.calculate_record_hash(row, "job")

                # 修正Job字段映射，确保所有必需字段都有正确的映射关系
                # 优先使用英文字段名，中文字段名作为备选
                job_type_cn = row.get('business_type_name') or row.get('business_type') or row.get('job_type_cn') or row.get('业务类型中文')
                job_date = row.get('job_date') or row.get('工作档日期')
                job_no = row.get('job_file_no') or row.get('job_no') or row.get('工作档号')
                vessel = row.get('vessel') or row.get('船名')
                voyage = row.get('voyage') or row.get('航次')
                pol_code = row.get('pol_code') or row.get('起运地')
                pod_code = row.get('pod_code') or row.get('卸货地')

                # 确保数值字段不为空或零，并进行数据清理和类型转换
                def safe_numeric_convert(value, default=0, decimal_places=None):
                    """安全的数值转换，处理None、空字符串、NaN、无穷大等情况，支持DECIMAL精度控制"""
                    if value is None or value == '' or is_nan_value(value):
                        return default
                    try:
                        # 尝试转换为float
                        float_val = float(value) if value != 0 else default
                        # 检查是否为无穷大或NaN
                        if not (float_val == float_val) or float_val == float('inf') or float_val == float('-inf'):
                            return default
                        # 如果指定了小数位数，进行四舍五入
                        if decimal_places is not None:
                            float_val = round(float_val, decimal_places)
                        return float_val
                    except (ValueError, TypeError):
                        return default

                bk_count = int(safe_numeric_convert(row.get('bk_count') or row.get('订舱数'), 0))
                rt = safe_numeric_convert(row.get('total_rt') or row.get('rt_sum') or row.get('rt') or row.get('RT总计'), 0, 3)  # DECIMAL(10,3)
                teu = safe_numeric_convert(row.get('total_teu') or row.get('teu_sum') or row.get('teu') or row.get('TEU总计'), 0, 3)  # DECIMAL(10,3)

                # 修正利润相关字段，使用安全转换，保持2位小数精度
                income = safe_numeric_convert(row.get('income') or row.get('收入'), 0, 2)  # DECIMAL(15,2)
                cost = safe_numeric_convert(row.get('cost') or row.get('成本'), 0, 2)  # DECIMAL(15,2)
                profit = safe_numeric_convert(row.get('profit') or row.get('job_profit') or row.get('毛利润'), 0, 2)  # DECIMAL(15,2)

                # 添加转运利润字段（新增）
                transhipment_profit = safe_numeric_convert(row.get('transhipment_profit') or row.get('转运利润'), 0, 2)  # DECIMAL(15,2)
                total_business_profit = safe_numeric_convert(row.get('total_business_profit') or row.get('业务总利润'), profit + transhipment_profit if profit is not None and transhipment_profit is not None else (profit or transhipment_profit or 0), 2)  # DECIMAL(15,2)

                operator_name = row.get('operator_name') or row.get('操作')
                job_handling_agent = row.get('job_handling_agent_name') or row.get('job_handling_agent') or row.get('工作档代理')

                # 修正指定货相关字段
                nomi_count = int(safe_numeric_convert(row.get('all_nominated_count') or row.get('nomi_count') or row.get('指定货票数'), 0))
                nomi_rt = safe_numeric_convert(row.get('all_nominated_rt') or row.get('nomi_rt_count') or row.get('nomi_rt') or row.get('指定货RT总和'), 0, 3)  # DECIMAL(10,3)

                # 修正集拼相关字段
                bill_count = int(safe_numeric_convert(row.get('bl_count') or row.get('bill_count') or row.get('提单数量'), 0))
                consolidation_20 = int(safe_numeric_convert(row.get('consol_20_count') or row.get('consol_20_sum') or row.get('consolidation_20') or row.get('集拼20尺柜数量'), 0))
                consolidation_40 = int(safe_numeric_convert(row.get('consol_40_count') or row.get('consol_40_sum') or row.get('consolidation_40') or row.get('集拼40尺柜数量'), 0))

                # 集拼判断：根据 is_consol 字段或集拼票数判断
                is_consol_flag = row.get('is_consol')
                if is_consol_flag is not None:
                    is_consolidation = bool(
                        is_consol_flag == 1 or
                        is_consol_flag == '是' or
                        str(is_consol_flag).lower() == 'true'
                    )
                else:
                    # 如果没有 is_consol 字段，根据集拼票数判断
                    consol_bk_count = safe_numeric_convert(
                        row.get('consol_bk_count') or row.get('集拼订舱数量'), 0
                    )
                    is_consolidation = bool(consol_bk_count >= 3)

                operator_department = (
                    row.get('operator_dept_name') or
                    row.get('operator_department') or
                    row.get('操作部门')
                )

                # 修正状态字段 - 转换为布尔值以匹配PostgreSQL的bool类型
                is_op_finished_value = row.get('is_op_finished')
                is_op_finished = bool(
                    is_op_finished_value == 1 or
                    is_op_finished_value is True or
                    is_op_finished_value == '是'
                )

                is_checked_value = row.get('is_checked')
                is_checked = bool(
                    is_checked_value == 1 or
                    is_checked_value is True or
                    is_checked_value == '是'
                )

                job_id = self.safe_int_convert(row.get('job_file_id') or row.get('job_id'))
                job_type_id = self.safe_int_convert(row.get('type_id') or row.get('job_type_id'))
                operator_id = self.safe_int_convert(row.get('operator_id'))
                etd_date = row.get('etd_date') or row.get('ETD')
                eta_date = row.get('eta_date') or row.get('ETA')

                # 使用asyncpg的execute方法逐条插入，因为asyncpg的executemany语法不同
                await connection.execute(insert_sql,
                    session_id, analysis_timestamp, self.pro2_system_id,
                    self.clean_data_for_postgres(job_type_cn),
                    self.clean_data_for_postgres(job_date),
                    self.clean_data_for_postgres(job_no),
                    self.clean_data_for_postgres(vessel),
                    self.clean_data_for_postgres(voyage),
                    self.clean_data_for_postgres(pol_code),
                    self.clean_data_for_postgres(pod_code),
                    self.clean_data_for_postgres(bk_count),
                    self.clean_data_for_postgres(rt),
                    self.clean_data_for_postgres(teu),
                    self.clean_data_for_postgres(income),
                    self.clean_data_for_postgres(cost),
                    self.clean_data_for_postgres(profit),
                    self.clean_data_for_postgres(transhipment_profit),
                    self.clean_data_for_postgres(total_business_profit),
                    self.clean_data_for_postgres(operator_name),
                    self.clean_data_for_postgres(job_handling_agent),
                    self.clean_data_for_postgres(nomi_count),
                    self.clean_data_for_postgres(nomi_rt),
                    is_consolidation,
                    self.clean_data_for_postgres(bill_count),
                    self.clean_data_for_postgres(consolidation_20),
                    self.clean_data_for_postgres(consolidation_40),
                    self.clean_data_for_postgres(operator_department),
                    is_op_finished,
                    is_checked,
                    self.clean_data_for_postgres(job_id),
                    self.clean_data_for_postgres(job_type_id),
                    self.clean_data_for_postgres(operator_id),
                    self.clean_data_for_postgres(etd_date),
                    self.clean_data_for_postgres(eta_date),
                    row_hash  # 添加哈希值
                )

            logger.info(f"已保存 {len(data)} 条Job详情数据，会话ID: {session_id}，系统ID: {self.pro2_system_id}")
    
    async def save_booking_details(self, data: List[Dict[str, Any]], session_id: str, analysis_timestamp: int):
        """保存Booking详情数据到t_booking_details表"""
        if not data:
            logger.warning("Booking数据为空，跳过保存")
            return

        async with get_postgres_connection_async(database=PG_DB_CMSDATA) as connection:
            insert_sql = """
            INSERT INTO t_booking_details (
                session_id, analysis_timestamp, created_at, pro2_system_id,
                job_type_cn, job_date, job_no, bkbl_no, client_name,
                vessel, voyage, job_pol, bill_pol, bill_pod,
                service_mode, lcl_rt, teu, air_weight, income, cost, profit,
                transhipment_profit, total_business_profit, is_freehand,
                salesman_name, salesman_department, salesman_id,
                operator_name, operator_department,
                coloader_name, job_handling_agent, bl_handling_agent, is_transhipment, transhipment_id,
                bkbl_id, job_id, job_type_id, operator_id, nomi_agent_name, data_hash
            ) VALUES (
                $1, $2, CURRENT_TIMESTAMP, $3,
                $4, $5, $6, $7, $8,
                $9, $10, $11, $12, $13,
                $14, $15, $16, $17, $18, $19, $20,
                $21, $22, $23,
                $24, $25, $26,
                $27, $28,
                $29, $30, $31, $32, $33,
                $34, $35, $36, $37, $38, $39
            ) ON CONFLICT (data_hash) DO UPDATE SET
                session_id = EXCLUDED.session_id,
                analysis_timestamp = EXCLUDED.analysis_timestamp,
                created_at = CURRENT_TIMESTAMP
            """

            for row in data:
                # 计算单条记录的哈希值
                row_hash = await self.calculate_record_hash(row, "booking")

                # 修正字段映射，确保所有必需字段都有正确的映射关系
                # 优先使用英文字段名，中文字段名作为备选
                job_type_cn = row.get('business_type_name') or row.get('job_type_cn') or row.get('业务类型中文')
                job_date = row.get('job_date') or row.get('工作档日期')
                job_no = row.get('job_file_no') or row.get('job_no') or row.get('工作档号')
                bkbl_no = row.get('business_no') or row.get('bkbl_no') or row.get('订舱/提单号')
                client_name = row.get('shipper_name') or row.get('client_name') or row.get('客户')
                vessel = row.get('vessel') or row.get('船名')
                voyage = row.get('voyage') or row.get('航次')

                # 修正港口字段映射
                job_pol = row.get('sailing_pol') or row.get('job_pol') or row.get('航次始发港')
                bill_pol = row.get('pol_code') or row.get('bkbl_pol') or row.get('bill_pol') or row.get('提单起运地')
                bill_pod = row.get('pod_code') or row.get('bkbl_pod') or row.get('bill_pod') or row.get('提单卸货地')

                service_mode_raw = row.get('service_mode') or row.get('服务模式')

                # 将service_mode从整数转换为字符串，以匹配PostgreSQL的varchar类型
                def convert_service_mode(mode):
                    """将service_mode从整数转换为字符串"""
                    if mode == 1:
                        return 'LCL'
                    elif mode == 2:
                        return 'FCL'
                    elif mode == 3:
                        return 'BUY-CONSOL'
                    elif mode == 4:
                        return 'AIR'
                    elif mode == 0:
                        return 'OTHER'
                    else:
                        # 如果已经是字符串，直接返回
                        return str(mode) if mode is not None else None

                service_mode = convert_service_mode(service_mode_raw)

                # 修正重量和TEU字段映射 - 根据service_mode决定使用哪个字段，增强数据清理
                def safe_numeric_convert_booking(value, default=0, decimal_places=None):
                    """安全的数值转换，处理None、空字符串、NaN、无穷大等情况，支持DECIMAL精度控制"""
                    if value is None or value == '' or is_nan_value(value):
                        return default
                    try:
                        # 尝试转换为float
                        float_val = float(value) if value != 0 else default
                        # 检查是否为无穷大或NaN
                        if not (float_val == float_val) or float_val == float('inf') or float_val == float('-inf'):
                            return default
                        # 如果指定了小数位数，进行四舍五入
                        if decimal_places is not None:
                            float_val = round(float_val, decimal_places)
                        return float_val
                    except (ValueError, TypeError):
                        return default

                rt_value = safe_numeric_convert_booking(row.get('lcl_rt') or row.get('rt'), 0, 3)

                # 根据服务模式映射字段
                if service_mode == 'LCL':
                    lcl_rt = rt_value
                    teu = 0  # LCL不计TEU
                    air_weight = None
                elif service_mode == 'FCL':
                    lcl_rt = None
                    # TEU = 计费数量/1000，参考data_format_converter.py的正确逻辑
                    teu_value = safe_numeric_convert_booking(row.get('teu'), 0, 3)
                    teu = teu_value if teu_value > 0 else (round(rt_value / 1000, 3) if rt_value > 0 else 0)
                    air_weight = None
                elif service_mode == 'AIR':
                    lcl_rt = None
                    teu = 0  # 空运不计TEU
                    air_weight = safe_numeric_convert_booking(row.get('air_weight'), rt_value, 3)
                else:
                    lcl_rt = rt_value if rt_value > 0 else None
                    teu = safe_numeric_convert_booking(row.get('teu'), 0, 3)
                    air_weight = safe_numeric_convert_booking(row.get('air_weight'), None, 3)

                income = safe_numeric_convert_booking(row.get('income') or row.get('收入'), 0, 2)  # DECIMAL(15,2)
                cost = safe_numeric_convert_booking(row.get('cost') or row.get('成本'), 0, 2)  # DECIMAL(15,2)
                profit = safe_numeric_convert_booking(row.get('profit') or row.get('利润'), 0, 2)  # DECIMAL(15,2)
                transhipment_profit = safe_numeric_convert_booking(row.get('transhipment_profit') or row.get('转运利润'), 0, 2)  # DECIMAL(15,2)
                total_business_profit = safe_numeric_convert_booking((profit + transhipment_profit) if profit is not None and transhipment_profit is not None else (profit if profit is not None else 0) + (transhipment_profit if transhipment_profit is not None else 0), 0, 2)  # DECIMAL(15,2)

                # 应用基本数据清理逻辑（轻量级，不进行复杂的业务逻辑转换）
                processed_row = apply_basic_data_cleaning(row)

                # 处理86021系统的特殊业务逻辑
                if self.pro2_system_id == 86021:
                    # 兼容不同的字段名称 - SQL查询返回的字段名是is_free_hand
                    original_is_freehand = row.get('is_free_hand', row.get('is_freehand', 0))
                    salesman_dept_name = row.get('salesman_dept_name', '') or row.get('salesman_department', '')

                    # 处理 is_freehand 字段的业务逻辑转换，转换为布尔值
                    if original_is_freehand == 0:
                        is_freehand = False
                    elif original_is_freehand == 1:
                        if salesman_dept_name == '指定货业务':
                            is_freehand = False  # 转换为指定货
                        else:
                            is_freehand = True
                    else:
                        is_freehand = False

                    # 对于从"指定货业务"部门转换来的指定货，清空salesman字段
                    if (
                        not is_freehand and
                        original_is_freehand == 1 and
                        salesman_dept_name == '指定货业务'
                    ):
                        salesman_name = None
                        salesman_department = None
                        salesman_id = None
                    else:
                        salesman_name = processed_row.get('salesman_name') or processed_row.get('salesman') or processed_row.get('业务员')
                        salesman_department = processed_row.get('salesman_department') or processed_row.get('salesman_dept_name') or processed_row.get('业务员部门')
                        salesman_id = self.safe_int_convert(processed_row.get('salesman_id') or processed_row.get('salesman_dept_id'))
                else:
                    # 其他系统的常规处理
                    is_freehand = processed_row.get('is_free_hand', 0)

                    # 确保is_freehand是布尔类型以匹配PostgreSQL的bool类型
                    if isinstance(is_freehand, str):
                        is_freehand = bool(is_freehand.lower() in ['true', '1', 'yes'])
                    elif is_freehand is None:
                        is_freehand = False
                    else:
                        is_freehand = bool(int(is_freehand))

                    salesman_name = processed_row.get('salesman_name') or processed_row.get('salesman') or processed_row.get('业务员')
                    salesman_department = processed_row.get('salesman_department') or processed_row.get('salesman_dept_name') or processed_row.get('业务员部门')
                    salesman_id = self.safe_int_convert(processed_row.get('salesman_id') or processed_row.get('salesman_dept_id'))

                # 根据业务规则提取 nomi_agent_name
                nomi_agent_name = self._extract_nomi_agent_name_with_priority(processed_row, is_freehand, row)

                operator_name = row.get('operator_name') or row.get('操作')
                operator_department = row.get('operator_dept_name') or row.get('operator_department') or row.get('操作部门')

                # 修复操作员信息为空的问题：如果operator_name或operator_department为空但operator_id存在，尝试通过ID获取
                operator_id = row.get('operator_id')
                if operator_id and (not operator_name or not operator_department):
                    try:
                        # 通过operator_id直接查询数据库获取操作员信息
                        from utils.basic.fb_conn import execute_pro2_query
                        sql = """
                            SELECT u.full_name, ud.name as dept_name
                            FROM users u
                            LEFT JOIN users_department ud ON u.dept_id = ud.dept_id
                            WHERE u.user_id = ? AND u.is_active = 1
                        """
                        result = execute_pro2_query(sql, (operator_id,), fetch_all=True)
                        if result and len(result) > 0:
                            if not operator_name and result[0][0]:
                                operator_name = result[0][0]  # full_name
                            if not operator_department and result[0][1]:
                                operator_department = result[0][1]  # dept_name
                            logger.debug(f"通过operator_id={operator_id}补充操作员信息: {operator_name}, {operator_department}")
                    except Exception as e:
                        logger.debug(f"通过operator_id={operator_id}获取操作员信息失败: {e}")

                coloader_name = row.get('coloader_name') or row.get('coloader')
                job_handling_agent = row.get('job_handling_agent_name') or row.get('job_handling_agent') or row.get('工作档代理')
                bl_handling_agent = row.get('handling_agent_name') or row.get('bl_handling_agent_name') or row.get('bl_handling_agent') or row.get('操作代理')

                is_transhipment_value = row.get('is_transhipment')
                is_transhipment = bool(
                    is_transhipment_value == 1 or
                    is_transhipment_value == '是' or
                    str(is_transhipment_value).lower() == 'true'
                )
                transhipment_id = self.safe_int_convert(row.get('transhipment_id') or row.get('转运ID'))

                # 修复bkbl_id字段映射 - 根据业务类型正确处理entity_id
                business_type = processed_row.get('business_type', '')
                entity_id = row.get('entity_id')

                # 根据业务类型处理entity_id，确保bkbl_id始终为字符串以匹配PostgreSQL的varchar类型
                if business_type == 'sea_import':
                    # 海运进口：entity_id是组合值 "job_file_id_bl_id"，使用完整字符串作为bkbl_id
                    bkbl_id = str(entity_id) if entity_id is not None else None
                else:
                    # 其他业务类型：entity_id转换为字符串
                    bkbl_id = str(entity_id) if entity_id is not None else None

                job_id = self.safe_int_convert(row.get('job_file_id'))
                job_type_id = self.safe_int_convert(row.get('type_id'))
                operator_id = self.safe_int_convert(row.get('operator_id'))

                # 使用asyncpg的execute方法逐条插入
                await connection.execute(insert_sql,
                    session_id, analysis_timestamp, self.pro2_system_id,
                    self.clean_data_for_postgres(job_type_cn),
                    self.clean_data_for_postgres(job_date),
                    self.clean_data_for_postgres(job_no),
                    self.clean_data_for_postgres(bkbl_no),
                    self.clean_data_for_postgres(client_name),
                    self.clean_data_for_postgres(vessel),
                    self.clean_data_for_postgres(voyage),
                    self.clean_data_for_postgres(job_pol),
                    self.clean_data_for_postgres(bill_pol),
                    self.clean_data_for_postgres(bill_pod),
                    self.clean_data_for_postgres(service_mode),
                    self.clean_data_for_postgres(lcl_rt),
                    self.clean_data_for_postgres(teu),
                    self.clean_data_for_postgres(air_weight),
                    self.clean_data_for_postgres(income),
                    self.clean_data_for_postgres(cost),
                    self.clean_data_for_postgres(profit),
                    self.clean_data_for_postgres(transhipment_profit),
                    self.clean_data_for_postgres(total_business_profit),
                    is_freehand,
                    self.clean_data_for_postgres(salesman_name),
                    self.clean_data_for_postgres(salesman_department),
                    self.clean_data_for_postgres(salesman_id),
                    self.clean_data_for_postgres(operator_name),
                    self.clean_data_for_postgres(operator_department),
                    self.clean_data_for_postgres(coloader_name),
                    self.clean_data_for_postgres(job_handling_agent),
                    self.clean_data_for_postgres(bl_handling_agent),
                    is_transhipment,
                    self.clean_data_for_postgres(transhipment_id),
                    self.clean_data_for_postgres(bkbl_id),
                    self.clean_data_for_postgres(job_id),
                    self.clean_data_for_postgres(job_type_id),
                    self.clean_data_for_postgres(operator_id),
                    self.clean_data_for_postgres(nomi_agent_name),
                    row_hash  # 添加哈希值
                )

            logger.info(f"已保存 {len(data)} 条Booking详情数据，会话ID: {session_id}，系统ID: {self.pro2_system_id}")
    
    async def process_period(self, period: AnalysisPeriod) -> bool:
        """处理单个分析周期"""
        try:
            logger.info(f"开始处理周期: {period.start_date} 至 {period.end_date} ({period.period_type})")
            
            session_id = await self.get_session_id()
            analysis_timestamp = int(datetime.now().timestamp())
            
            # 获取新数据
            start_str = period.start_date.strftime('%Y-%m-%d')
            end_str = period.end_date.strftime('%Y-%m-%d')
            
            # 获取Job数据
            job_result = await get_job_details_with_transhipment(start_str, end_str)
            job_data = job_result.get('data', []) if isinstance(job_result, dict) else []
            logger.info(f"获取到Job数据: {len(job_data)} 条记录")
            
            # 使用记录级哈希比较
            changed_job_records = await self.get_changed_records(job_data, "job", period)
            if changed_job_records:
                logger.info(f"Job数据有变更，新增变化记录: {len(changed_job_records)} 条（总数据 {len(job_data)} 条）")
                await self.save_job_details(changed_job_records, session_id, analysis_timestamp)
            else:
                logger.info("Job数据无变更，跳过保存")
            
            # 获取Booking数据
            booking_result = await get_sea_air_profit_with_transhipment(start_str, end_str)
            booking_data = booking_result.get('data', []) if isinstance(booking_result, dict) else []
            
            # 使用记录级哈希比较
            changed_booking_records = await self.get_changed_records(booking_data, "booking", period)
            if changed_booking_records:
                logger.info(f"Booking数据有变更，新增变化记录: {len(changed_booking_records)} 条（总数据 {len(booking_data)} 条）")
                await self.save_booking_details(changed_booking_records, session_id, analysis_timestamp)
            else:
                logger.info("Booking数据无变更，跳过保存")
                
            # 计算整体哈希用于检查日志
            new_job_hash = await self.calculate_data_hash(job_data, "job") if job_data else "empty_data"
            new_booking_hash = await self.calculate_data_hash(booking_data, "booking") if booking_data else "empty_data"
            
            # 更新最后检查时间和哈希值到数据库
            await self.update_last_check_time(period, datetime.now(), new_job_hash, new_booking_hash)
            
            return True
            
        except Exception as e:
            logger.error(f"处理周期失败 {period.start_date}-{period.end_date}: {e}")
            return False
            
    async def process_periods_async(self, periods: List[AnalysisPeriod], current_dt: datetime) -> Tuple[int, int]:
        """异步处理多个周期，按频率分组并发执行，支持分层执行时间检查"""
        # 按检查频率分组
        groups = {
            "3months": [],
            "1month": [],
            "1week": [],
            "1day": []
        }

        # 筛选需要检查的周期并分组，同时检查执行时间
        for period in periods:
            # 首先检查是否需要检查（基于时间间隔）
            if await self.needs_check(period, current_dt):
                # 然后检查是否在允许的执行时间内（基于周期类型）
                if await self.should_run_analysis(current_dt, period.period_type):
                    groups[period.check_frequency].append(period)
                else:
                    logger.debug(f"周期 {period.period_type} ({period.start_date}~{period.end_date}) 不在执行时间窗口内，跳过")

        processed_count = 0
        failed_count = 0

        # 按优先级异步处理不同频率的分组（每天检查优先级最高）
        for frequency in ["1day", "1week", "1month", "3months"]:
            if groups[frequency]:
                logger.info(f"异步处理 {frequency} 频率的 {len(groups[frequency])} 个周期")

                # 创建异步任务列表
                tasks = [self.process_period(period) for period in groups[frequency]]

                # 并发执行，最多同时处理3个周期以避免数据库压力过大
                semaphore = asyncio.Semaphore(3)

                async def process_with_semaphore(task):
                    async with semaphore:
                        return await task

                # 执行所有任务
                results = await asyncio.gather(
                    *[process_with_semaphore(task) for task in tasks],
                    return_exceptions=True
                )

                # 统计结果
                for result in results:
                    if isinstance(result, Exception):
                        logger.error(f"异步处理异常: {result}")
                        failed_count += 1
                    elif result:
                        processed_count += 1
                    else:
                        failed_count += 1

                # 每组之间稍作休息
                await asyncio.sleep(1)

        return processed_count, failed_count
    
    async def run_scheduled_analysis(self):
        """运行计划的分析任务（异步优化版本）- 支持分层执行时间"""
        if self.is_running:
            logger.warning("分析任务正在运行中，跳过本次执行")
            return

        current_dt = datetime.now()

        # 不再进行全局时间检查，而是在process_periods_async中按周期类型检查
        # 这样可以支持不同周期类型的不同执行时间策略

        self.is_running = True
        logger.info("开始执行周期性利润数据分析（分层执行时间模式）")

        try:
            periods = self.get_analysis_periods()
            logger.info(f"总共生成 {len(periods)} 个分析周期")

            # 异步处理所有周期，内部会根据周期类型检查执行时间
            processed_count, failed_count = await self.process_periods_async(periods, current_dt)

            logger.info(f"周期性分析完成：处理 {processed_count} 个周期，失败 {failed_count} 个")

        except Exception as e:
            logger.error(f"周期性分析异常: {e}")
        finally:
            self.is_running = False
            
    async def start_scheduler(self):
        """启动调度器"""
        logger.info("利润数据调度器启动")
        
        # 确保必要的数据库表存在
        await self.ensure_required_tables_exist()
        
        while True:
            try:
                await self.run_scheduled_analysis()
                # 每小时检查一次
                await asyncio.sleep(3600)
            except Exception as e:
                logger.error(f"调度器异常: {e}")
                await asyncio.sleep(300)  # 错误时5分钟后重试