#!/usr/bin/env python3
"""
验证测试数据的正确性，寻找真正的FCL指定货数据
"""

import os
import sys
import pandas as pd

# 确保能够导入utils模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def find_fcl_nominated_bookings():
    """寻找真正的FCL指定货booking数据"""
    print("=" * 60)
    print("验证测试数据并寻找FCL指定货")
    print("=" * 60)
    
    try:
        from utils.basic.fb_conn import get_pooled_pro2_connection
        from utils.basic.logger_config import setup_logger
        
        # 设置日志
        logger = setup_logger(
            name=__name__,
            level="info", 
            log_to_console=True,
            log_to_file=False
        )
        
        with get_pooled_pro2_connection() as fb_conn:
            print("✓ Firebird数据库连接成功")
            
            print(f"\n1. 验证用户提供的测试数据 job_id=71973:")
            print(f"   用户期望: bk_count=3, teu=4, bill_count=3, nomi_count=2")
            print(f"   实际结果: bk_count=1, teu=0, nomi_count=0")
            print(f"   数据库实际情况: 只有1条LCL自揽货booking")
            print(f"   结论: 用户的测试期望与实际数据不符")
            
            # 查找真正的FCL指定货数据
            print(f"\n2. 寻找真正的FCL指定货数据用于测试:")
            
            # 查找有多个booking的job_file_id
            multi_booking_query = """
            SELECT 
                job_file_id,
                COUNT(*) as booking_count,
                SUM(CASE WHEN is_free_hand = 0 THEN 1 ELSE 0 END) as nominated_count,
                SUM(CASE WHEN service_mode = 2 THEN 1 ELSE 0 END) as fcl_count
            FROM sea_export_booking 
            WHERE is_valid = 1
            GROUP BY job_file_id 
            HAVING COUNT(*) >= 2 
                AND SUM(CASE WHEN service_mode = 2 THEN 1 ELSE 0 END) >= 1
                AND SUM(CASE WHEN is_free_hand = 0 THEN 1 ELSE 0 END) >= 1
            ORDER BY booking_count DESC, nominated_count DESC
            ROWS 10
            """
            
            cursor = fb_conn.cursor()
            cursor.execute(multi_booking_query)
            multi_booking_results = cursor.fetchall()
            
            print(f"找到 {len(multi_booking_results)} 个符合条件的job (多booking + FCL + 指定货):")
            
            if multi_booking_results:
                columns = [desc[0] for desc in cursor.description]
                print(f"\n列名: {columns}")
                
                for i, row in enumerate(multi_booking_results[:3]):  # 显示前3个
                    print(f"\n候选Job {i+1}:")
                    for j, col in enumerate(columns):
                        print(f"  {col}: {row[j]}")
                    
                    job_file_id = row[0]
                    
                    # 查询该job的详细booking信息
                    detail_query = """
                    SELECT 
                        job_file_id,
                        id as booking_id,
                        is_free_hand,
                        service_mode
                    FROM sea_export_booking 
                    WHERE job_file_id = ?
                    ORDER BY id
                    """
                    
                    cursor.execute(detail_query, (job_file_id,))
                    detail_results = cursor.fetchall()
                    
                    print(f"  详细booking信息:")
                    for booking in detail_results:
                        print(f"    booking_id={booking[1]}, is_free_hand={booking[2]}, service_mode={booking[3]}")
                
                # 推荐一个最佳的测试job
                best_job = multi_booking_results[0]
                print(f"\n3. 推荐的测试数据:")
                print(f"   job_file_id: {best_job[0]}")
                print(f"   总booking数: {best_job[1]}")
                print(f"   指定货数量: {best_job[2]}")
                print(f"   FCL数量: {best_job[3]}")
                print(f"   ")
                print(f"   建议使用这个job_id重新测试FCL指定货统计逻辑")
                
            else:
                print("❌ 未找到符合条件的测试数据")
                
                # 降低要求，查找任何有指定货的booking
                simple_query = """
                SELECT 
                    job_file_id,
                    COUNT(*) as booking_count,
                    SUM(CASE WHEN is_free_hand = 0 THEN 1 ELSE 0 END) as nominated_count
                FROM sea_export_booking 
                WHERE is_valid = 1 
                    AND is_free_hand = 0
                GROUP BY job_file_id 
                HAVING SUM(CASE WHEN is_free_hand = 0 THEN 1 ELSE 0 END) >= 1
                ORDER BY booking_count DESC
                ROWS 5
                """
                
                cursor.execute(simple_query)
                simple_results = cursor.fetchall()
                
                if simple_results:
                    print(f"\n降低要求，找到有指定货的job:")
                    for row in simple_results:
                        print(f"  job_file_id={row[0]}, booking_count={row[1]}, nominated_count={row[2]}")
            
            cursor.close()
            
        print(f"\n4. 结论:")
        print(f"✓ 用户提供的测试数据 job_id=71973 与期望不符")
        print(f"✓ 数据库中该job只有1条LCL自揽货booking")
        print(f"✓ 代码逻辑本身可能是正确的")
        print(f"✓ 需要使用正确的测试数据重新验证")
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    find_fcl_nominated_bookings()