#!/usr/bin/env python3
"""
直接查询firebird数据库验证job_file_id=71973的数据
"""

import os
from utils.basic.fb_conn import get_pooled_pro2_connection

def test_job_71973_direct():
    """直接查询job_file_id=71973的数据"""
    
    print("=" * 80)
    print("直接查询firebird数据库 - job_file_id=71973")
    print("=" * 80)
    
    try:
        with get_pooled_pro2_connection() as conn:
            cursor = conn.cursor()
            
            # 1. 查询基本booking信息
            print("1. 基本booking信息:")
            print("-" * 40)
            
            booking_query = """
            SELECT 
                seb.id as booking_id,
                seb.job_file_id,
                seb.booking_no,
                seb.service_mode,
                seb.is_free_hand,
                seb.cargo_type,
                seb.is_valid,
                jf.job_file_no,
                jf.is_active
            FROM sea_export_booking seb
            LEFT JOIN job_file jf ON seb.job_file_id = jf.id
            WHERE seb.job_file_id = 71973
            AND seb.is_valid = 1
            ORDER BY seb.id
            """
            
            cursor.execute(booking_query)
            booking_results = cursor.fetchall()
            
            print(f"找到 {len(booking_results)} 条有效booking记录:")
            
            if booking_results:
                columns = [desc[0] for desc in cursor.description]
                
                for i, row in enumerate(booking_results):
                    print(f"\nBooking {i+1}:")
                    for j, col in enumerate(columns):
                        print(f"  {col}: {row[j]}")
                
                # 统计
                total_bookings = len(booking_results)
                nominated_bookings = sum(1 for row in booking_results if row[4] == 0)  # is_free_hand = 0
                
                print(f"\n统计:")
                print(f"  总booking数: {total_bookings}")
                print(f"  指定货booking数 (is_free_hand=0): {nominated_bookings}")
                print(f"  期望bk_count=3, 实际={total_bookings} {'✓' if total_bookings == 3 else '✗'}")
                print(f"  期望nomi_count=2, 实际={nominated_bookings} {'✓' if nominated_bookings == 2 else '✗'}")
            
            # 2. 查询TEU信息
            print("\n" + "=" * 40)
            print("2. TEU信息:")
            print("-" * 40)
            
            teu_query = """
            SELECT 
                seb.id as booking_id,
                seb.booking_no,
                sebc.quantity,
                cs.name as container_size,
                CASE
                    WHEN cs.name LIKE '%20%' THEN sebc.quantity
                    WHEN cs.name LIKE '%40%' THEN sebc.quantity * 2
                    WHEN cs.name LIKE '%45%' THEN sebc.quantity * 2
                    ELSE 0
                END as teu_value
            FROM sea_export_booking seb
            LEFT JOIN sea_export_bk_container sebc ON seb.id = sebc.bk_id
            LEFT JOIN container_size cs ON sebc.size_id = cs.id
            WHERE seb.job_file_id = 71973
            AND seb.is_valid = 1
            AND sebc.quantity > 0
            ORDER BY seb.id, sebc.id
            """
            
            cursor.execute(teu_query)
            teu_results = cursor.fetchall()
            
            print(f"找到 {len(teu_results)} 条容器记录:")
            
            total_teu = 0
            if teu_results:
                teu_columns = [desc[0] for desc in cursor.description]
                
                for i, row in enumerate(teu_results):
                    print(f"\nContainer {i+1}:")
                    for j, col in enumerate(teu_columns):
                        print(f"  {col}: {row[j]}")
                    total_teu += row[4] if row[4] else 0
                
                print(f"\n统计:")
                print(f"  总TEU: {total_teu}")
                print(f"  期望teu=4, 实际={total_teu} {'✓' if total_teu == 4 else '✗'}")
            
            # 3. 查询BL信息
            print("\n" + "=" * 40)
            print("3. BL信息:")
            print("-" * 40)
            
            bl_query = """
            SELECT DISTINCT
                sebb.bl_id,
                sebl.bl_no,
                sebl.is_valid as bl_is_valid
            FROM sea_export_booking seb
            LEFT JOIN sea_export_bk_bl sebb ON seb.id = sebb.bk_id
            LEFT JOIN sea_export_bl sebl ON sebb.bl_id = sebl.id
            WHERE seb.job_file_id = 71973
            AND seb.is_valid = 1
            AND (sebl.is_valid = 1 OR sebl.is_valid IS NULL)
            AND sebb.bl_id IS NOT NULL
            ORDER BY sebb.bl_id
            """
            
            cursor.execute(bl_query)
            bl_results = cursor.fetchall()
            
            print(f"找到 {len(bl_results)} 条唯一BL记录:")
            
            if bl_results:
                bl_columns = [desc[0] for desc in cursor.description]
                
                for i, row in enumerate(bl_results):
                    print(f"\nBL {i+1}:")
                    for j, col in enumerate(bl_columns):
                        print(f"  {col}: {row[j]}")
                
                bl_count = len(bl_results)
                print(f"\n统计:")
                print(f"  唯一BL数量: {bl_count}")
                print(f"  期望bill_count=3, 实际={bl_count} {'✓' if bl_count == 3 else '✗'}")
            
            # 4. 查询job基本信息
            print("\n" + "=" * 40)
            print("4. Job基本信息:")
            print("-" * 40)
            
            job_query = """
            SELECT 
                jf.id as job_file_id,
                jf.job_file_no,
                jf.is_active,
                jf.is_checked,
                jf.is_op_finished,
                icn.job_date
            FROM job_file jf
            LEFT JOIN invoice_cost_note icn ON jf.id = icn.job_file_id
            WHERE jf.id = 71973
            AND jf.is_active = 1
            AND icn.is_valid = 1
            """
            
            cursor.execute(job_query)
            job_results = cursor.fetchall()
            
            if job_results:
                job_columns = [desc[0] for desc in cursor.description]
                
                for i, row in enumerate(job_results):
                    print(f"\nJob {i+1}:")
                    for j, col in enumerate(job_columns):
                        print(f"  {col}: {row[j]}")
            
            cursor.close()
            
    except Exception as e:
        print(f"查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 设置环境变量
    os.environ['PRO2_SYSTEM_ID_QD'] = '86532'
    os.environ['LOCATION'] = 'QD'
    
    test_job_71973_direct()
