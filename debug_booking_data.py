#!/usr/bin/env python3
"""
调试booking数据结构和service_mode过滤
"""

import os
import sys
import pandas as pd

# 确保能够导入utils模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_booking_data_structure():
    """调试booking数据结构和service_mode过滤问题"""
    print("=" * 60)
    print("调试booking数据结构和service_mode过滤")
    print("=" * 60)
    
    try:
        from utils.basic.fb_conn import get_pooled_pro2_connection
        from utils.basic.logger_config import setup_logger
        
        # 设置日志
        logger = setup_logger(
            name=__name__,
            level="info", 
            log_to_console=True,
            log_to_file=False
        )
        
        # 获取Firebird连接
        print("连接Firebird数据库...")
        
        with get_pooled_pro2_connection() as fb_conn:
            print("✓ Firebird数据库连接成功")
            
            # 查询job_id=71973的所有booking数据
            print(f"\n查询 job_id=71973 的所有booking数据...")
            
            # 首先查询基本的booking信息 - 使用正确的列名
            basic_query = """
            SELECT 
                job_file_id,
                id as booking_id,
                is_free_hand,
                service_mode
            FROM sea_export_booking 
            WHERE job_file_id = 71973
            ORDER BY id
            """
            
            cursor = fb_conn.cursor()
            cursor.execute(basic_query)
            booking_results = cursor.fetchall()
            
            print(f"查询到 {len(booking_results)} 条booking记录:")
            
            if booking_results:
                # 显示列名
                columns = [desc[0] for desc in cursor.description]
                print(f"\n列名: {columns}")
                
                # 显示每条记录
                for i, row in enumerate(booking_results):
                    print(f"\nBooking {i+1}:")
                    for j, col in enumerate(columns):
                        print(f"  {col}: {row[j]}")
            else:
                print("❌ 未找到job_id=71973的booking数据")
                
                # 尝试查询相近的job_id
                print("\n查询相近的job_id...")
                nearby_query = """
                SELECT DISTINCT job_file_id 
                FROM sea_export_booking 
                WHERE job_file_id BETWEEN 71970 AND 71975
                ORDER BY job_file_id
                """
                cursor.execute(nearby_query)
                nearby_results = cursor.fetchall()
                print(f"相近的job_id: {[row[0] for row in nearby_results]}")
                
            cursor.close()
            
            # 如果有数据，分析service_mode分布
            if booking_results:
                print(f"\n分析service_mode分布:")
                service_modes = {}
                fcl_count = 0
                lcl_count = 0
                
                for row in booking_results:
                    service_mode = row[3]  # service_mode是第4列
                    
                    if service_mode not in service_modes:
                        service_modes[service_mode] = 0
                    service_modes[service_mode] += 1
                    
                    if service_mode == 2:  # FCL
                        fcl_count += 1
                    elif service_mode in [1, 3]:  # LCL
                        lcl_count += 1
                
                print(f"  FCL (service_mode=2): {fcl_count} 条")
                print(f"  LCL (service_mode=1,3): {lcl_count} 条")
                print(f"  详细分布: {dict(service_modes)}")
                
                # 分析is_free_hand分布
                print(f"\n分析is_free_hand分布:")
                nominated_count = 0
                self_sourced_count = 0
                
                for row in booking_results:
                    is_free_hand = row[2]  # is_free_hand是第3列
                    if is_free_hand == 0:
                        nominated_count += 1
                    elif is_free_hand == 1:
                        self_sourced_count += 1
                        
                print(f"  指定货 (is_free_hand=0): {nominated_count} 条")
                print(f"  自揽货 (is_free_hand=1): {self_sourced_count} 条")
                
                # TEU信息需要从其他表获取，先跳过
                print(f"\n注意: TEU信息需要从相关联的表获取，这里先显示基础信息")
                
            # 测试SQL_SEA_EXPORT_PROFIT查询
            print(f"\n测试SQL_SEA_EXPORT_PROFIT查询 (只查FCL):")
            fcl_query = """
            SELECT 
                seb.job_file_id,
                seb.id as booking_id,
                seb.is_free_hand,
                seb.service_mode
            FROM sea_export_booking seb
            WHERE seb.job_file_id = 71973
            AND seb.service_mode = 2
            ORDER BY seb.id
            """
            
            cursor = fb_conn.cursor()
            cursor.execute(fcl_query)
            fcl_results = cursor.fetchall()
            
            print(f"FCL查询结果: {len(fcl_results)} 条记录")
            if fcl_results:
                columns = [desc[0] for desc in cursor.description]
                for i, row in enumerate(fcl_results):
                    print(f"  FCL Record {i+1}:")
                    for j, col in enumerate(columns):
                        print(f"    {col}: {row[j]}")
            
            cursor.close()
        
        print(f"\n结论分析:")
        print(f"1. 如果总booking数 > FCL查询结果数，说明有部分booking不是FCL")
        print(f"2. 如果FCL查询结果数 = 1，说明SQL_SEA_EXPORT_PROFIT只能查到1条FCL记录")
        print(f"3. 需要检查其他2条booking的service_mode是否为1或3 (LCL)")
        print(f"4. 统计逻辑可能需要同时处理FCL和LCL的booking")
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_booking_data_structure()