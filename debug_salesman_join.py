#!/usr/bin/env python3
"""
调试salesman JOIN问题
"""

import os
from utils.basic.fb_conn import get_pooled_pro2_connection

def debug_salesman_join():
    """调试salesman JOIN问题"""
    
    print("=" * 80)
    print("调试salesman JOIN问题 - job_file_id=71973")
    print("=" * 80)
    
    try:
        with get_pooled_pro2_connection() as conn:
            cursor = conn.cursor()
            
            # 1. 查询booking记录的salesman信息
            print("1. Booking记录的salesman信息:")
            print("-" * 40)
            
            salesman_query = """
            SELECT 
                seb.id as booking_id,
                seb.booking_no,
                seb.is_free_hand,
                seb.salesman_id,
                us.full_name as salesman_name,
                us.dept_id,
                usd.name as salesman_dept_name
            FROM sea_export_booking seb
            LEFT JOIN users us ON seb.salesman_id = us.user_id
            LEFT JOIN users_department usd ON us.dept_id = usd.dept_id
            WHERE seb.job_file_id = 71973
            AND seb.is_valid = 1
            ORDER BY seb.id
            """
            
            cursor.execute(salesman_query)
            salesman_results = cursor.fetchall()
            
            print(f"找到 {len(salesman_results)} 条booking记录:")
            
            if salesman_results:
                salesman_columns = [desc[0] for desc in cursor.description]
                
                for i, row in enumerate(salesman_results):
                    print(f"\nBooking {i+1}:")
                    for j, col in enumerate(salesman_columns):
                        print(f"  {col}: {row[j]}")
            
            # 2. 测试INNER JOIN vs LEFT JOIN的差异
            print("\n" + "=" * 40)
            print("2. INNER JOIN vs LEFT JOIN测试:")
            print("-" * 40)
            
            # INNER JOIN测试
            inner_join_query = """
            SELECT 
                seb.id as booking_id,
                seb.booking_no,
                seb.is_free_hand,
                us.full_name as salesman_name,
                usd.name as salesman_dept_name
            FROM sea_export_booking seb
            INNER JOIN users us ON seb.salesman_id = us.user_id
            INNER JOIN users_department usd ON us.dept_id = usd.dept_id
            WHERE seb.job_file_id = 71973
            AND seb.is_valid = 1
            ORDER BY seb.id
            """
            
            cursor.execute(inner_join_query)
            inner_join_results = cursor.fetchall()
            
            print(f"INNER JOIN结果: {len(inner_join_results)} 条记录")
            
            if inner_join_results:
                for i, row in enumerate(inner_join_results):
                    print(f"  Booking {i+1}: ID={row[0]}, is_free_hand={row[2]}")
            
            # LEFT JOIN测试
            left_join_query = """
            SELECT 
                seb.id as booking_id,
                seb.booking_no,
                seb.is_free_hand,
                us.full_name as salesman_name,
                usd.name as salesman_dept_name
            FROM sea_export_booking seb
            LEFT JOIN users us ON seb.salesman_id = us.user_id
            LEFT JOIN users_department usd ON us.dept_id = usd.dept_id
            WHERE seb.job_file_id = 71973
            AND seb.is_valid = 1
            ORDER BY seb.id
            """
            
            cursor.execute(left_join_query)
            left_join_results = cursor.fetchall()
            
            print(f"LEFT JOIN结果: {len(left_join_results)} 条记录")
            
            if left_join_results:
                for i, row in enumerate(left_join_results):
                    print(f"  Booking {i+1}: ID={row[0]}, is_free_hand={row[2]}")
            
            # 3. 分析差异
            print("\n" + "=" * 40)
            print("3. 差异分析:")
            print("-" * 40)
            
            if len(inner_join_results) != len(left_join_results):
                print("⚠️  发现问题：INNER JOIN和LEFT JOIN结果不一致！")
                print(f"INNER JOIN: {len(inner_join_results)} 条记录")
                print(f"LEFT JOIN: {len(left_join_results)} 条记录")
                
                # 找出被INNER JOIN排除的记录
                inner_ids = set(row[0] for row in inner_join_results)
                left_ids = set(row[0] for row in left_join_results)
                excluded_ids = left_ids - inner_ids
                
                if excluded_ids:
                    print(f"\n被INNER JOIN排除的booking ID: {excluded_ids}")
                    
                    for excluded_id in excluded_ids:
                        for row in left_join_results:
                            if row[0] == excluded_id:
                                print(f"\n被排除的booking {excluded_id}:")
                                print(f"  booking_no: {row[1]}")
                                print(f"  is_free_hand: {row[2]}")
                                print(f"  salesman_name: {row[3]}")
                                print(f"  salesman_dept_name: {row[4]}")
                                break
                
                # 统计is_free_hand=0的记录
                inner_nominated = sum(1 for row in inner_join_results if row[2] == 0)
                left_nominated = sum(1 for row in left_join_results if row[2] == 0)
                
                print(f"\nINNER JOIN中is_free_hand=0的记录: {inner_nominated}")
                print(f"LEFT JOIN中is_free_hand=0的记录: {left_nominated}")
                
                if inner_nominated != left_nominated:
                    print("🎯 这就是问题所在！INNER JOIN排除了is_free_hand=0的记录！")
                
            else:
                print("✓ INNER JOIN和LEFT JOIN结果一致")
            
            cursor.close()
            
    except Exception as e:
        print(f"查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 设置环境变量
    os.environ['PRO2_SYSTEM_ID_QD'] = '86532'
    os.environ['LOCATION'] = 'QD'
    
    debug_salesman_join()
