{"permissions": {"allow": ["Bash(find:*)", "Bash(rg:*)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(true)", "Bash(rm:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(uv run:*)", "Bash(PRO2_SYSTEM_ID=86532 uv run python -c \"\nimport asyncio\nimport os\nimport sys\nsys.path.append(''.'')\nfrom utils.basic.data_conn_unified import get_mysql_connection, MYSQL_DB_MCP\nimport aiomysql\n\nasync def test():\n    try:\n        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:\n            async with connection.cursor(aiomysql.DictCursor) as cursor:\n                await cursor.execute(''SELECT COUNT(*) as count FROM t_job_details WHERE pro2_system_id = %s'', (86532,))\n                result = await cursor.fetchone()\n                print(f''Job details count: {result[\"\"count\"\"]}'')\n                \n                await cursor.execute(''SELECT COUNT(*) as count FROM t_booking_details WHERE pro2_system_id = %s'', (86532,))\n                result = await cursor.fetchone()\n                print(f''Booking details count: {result[\"\"count\"\"]}'')\n    except Exception as e:\n        print(f''Error: {e}'')\n\nasyncio.run(test())\n\")", "Bash(PRO2_SYSTEM_ID=86532 uv run python update_data_hash.py --dry-run)"], "deny": []}}