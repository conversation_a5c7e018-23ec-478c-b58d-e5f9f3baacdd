#!/usr/bin/env python3
"""
测试插入行为 - 确认每次都新增记录
"""

import asyncio
import os
from datetime import datetime, date
from utils.app.profit_data_scheduler_pg import ProfitDataScheduler
from utils.basic.logger_config import setup_logger

# 设置日志
logger = setup_logger(
    name=__name__,
    level="info",
    log_to_console=True,
    log_to_file=True
)

async def test_insert_behavior():
    """测试插入行为"""
    
    # 设置环境变量
    os.environ['PRO2_SYSTEM_ID_QD'] = '86532'
    os.environ['LOCATION'] = 'QD'
    os.environ['FORCE_IMMEDIATE_EXECUTION'] = 'true'
    
    logger.info("开始测试插入行为...")
    
    try:
        scheduler = ProfitDataScheduler()
        
        # 确保表存在
        await scheduler.ensure_required_tables_exist()
        
        # 手动指定2025年4月的周期
        from utils.app.profit_data_scheduler_pg import AnalysisPeriod
        target_period = AnalysisPeriod(
            start_date=date(2025, 4, 1),
            end_date=date(2025, 4, 30),
            period_type='monthly',
            check_frequency='daily'
        )
        
        logger.info(f"测试周期: {target_period.start_date} 到 {target_period.end_date}")
        
        # 查询修改前的记录数
        from utils.basic.pg_conn import get_postgres_connection_async
        
        async with get_postgres_connection_async() as pg_conn:
            # 查询job_file_id=71973的记录数
            before_count = await pg_conn.fetchval("""
                SELECT COUNT(*) FROM t_job_details WHERE job_id = $1
            """, 71973)
            
            logger.info(f"修改前job_file_id=71973的记录数: {before_count}")
        
        # 获取会话ID
        session_id = await scheduler.get_session_id()
        analysis_timestamp = int(datetime.now().timestamp())
        
        logger.info(f"会话ID: {session_id}")
        
        # 获取Job数据
        from utils.database.db_pro2_basic import get_job_details_with_transhipment
        
        logger.info("获取Job数据...")
        job_result = await get_job_details_with_transhipment(
            target_period.start_date.strftime('%Y-%m-%d'),
            target_period.end_date.strftime('%Y-%m-%d')
        )
        
        if not job_result or not job_result.get('data'):
            logger.warning("未获取到Job数据")
            return
        
        job_data = job_result['data']
        logger.info(f"获取到 {len(job_data)} 条Job记录")
        
        # 查找job_file_id=71973的记录
        target_job = None
        for record in job_data:
            if record.get('job_file_id') == 71973:
                target_job = record
                break
        
        if target_job:
            logger.info("找到job_file_id=71973的记录，保存到PostgreSQL...")
            
            # 保存数据到PostgreSQL
            await scheduler.save_job_details([target_job], session_id, analysis_timestamp)
            logger.info("Job数据保存完成")
            
            # 查询修改后的记录数
            async with get_postgres_connection_async() as pg_conn:
                after_count = await pg_conn.fetchval("""
                    SELECT COUNT(*) FROM t_job_details WHERE job_id = $1
                """, 71973)
                
                logger.info(f"修改后job_file_id=71973的记录数: {after_count}")
                
                # 查询最新的几条记录
                latest_records = await pg_conn.fetch("""
                    SELECT session_id, analysis_timestamp, created_at, bk_count, teu, nomi_count
                    FROM t_job_details 
                    WHERE job_id = $1 
                    ORDER BY created_at DESC 
                    LIMIT 3
                """, 71973)
                
                logger.info("最新的3条记录:")
                for i, record in enumerate(latest_records):
                    logger.info(f"  记录{i+1}: session_id={record['session_id']}, "
                              f"analysis_timestamp={record['analysis_timestamp']}, "
                              f"created_at={record['created_at']}, "
                              f"bk_count={record['bk_count']}, "
                              f"teu={record['teu']}, "
                              f"nomi_count={record['nomi_count']}")
                
                # 验证是否新增了记录
                if after_count > before_count:
                    logger.info(f"✓ 成功新增了 {after_count - before_count} 条记录")
                elif after_count == before_count:
                    logger.warning("⚠️  记录数量没有变化，可能是重复插入被阻止了")
                else:
                    logger.error("✗ 记录数量减少了，这不应该发生")
                
        else:
            logger.warning("未找到job_file_id=71973的记录")
        
        logger.info("测试完成")
        
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(test_insert_behavior())
