#!/usr/bin/env python3
"""
调试job_file_id=71973的booking数据
"""

import os
import sys
sys.path.append('/Users/<USER>/x-code-files/mcp-cms')

from utils.basic.db_pro2_connection import get_pooled_pro2_connection

def debug_job_71973_booking():
    """调试job_file_id=71973的booking数据"""
    
    print("=" * 80)
    print("调试job_file_id=71973的booking数据")
    print("=" * 80)
    
    try:
        with get_pooled_pro2_connection() as fb_conn:
            cursor = fb_conn.cursor()
            
            # 查询job_file_id=71973的所有booking记录
            booking_query = """
            SELECT 
                seb.id as booking_id,
                seb.job_file_id,
                seb.booking_no,
                seb.service_mode,
                seb.is_free_hand,
                seb.cargo_type,
                seb.is_valid,
                jf.job_file_no,
                jf.is_active,
                us.full_name as salesman_name,
                usd.name as salesman_dept_name
            FROM sea_export_booking seb
            LEFT JOIN job_file jf ON seb.job_file_id = jf.id
            LEFT JOIN users us ON seb.salesman_id = us.user_id
            LEFT JOIN users_department usd ON us.dept_id = usd.dept_id
            WHERE seb.job_file_id = 71973
            ORDER BY seb.id
            """
            
            cursor.execute(booking_query)
            booking_results = cursor.fetchall()
            
            print(f"找到 {len(booking_results)} 条booking记录:")
            print()
            
            if booking_results:
                columns = [desc[0] for desc in cursor.description]
                
                for i, row in enumerate(booking_results):
                    print(f"Booking {i+1}:")
                    for j, col in enumerate(columns):
                        print(f"  {col}: {row[j]}")
                    print()
                
                # 统计关键字段
                print("=" * 60)
                print("统计分析:")
                print("=" * 60)
                
                total_bookings = len(booking_results)
                fcl_bookings = sum(1 for row in booking_results if row[3] == 2)  # service_mode = 2
                lcl_bookings = sum(1 for row in booking_results if row[3] in [1, 3])  # service_mode = 1 or 3
                nominated_bookings = sum(1 for row in booking_results if row[4] == 0)  # is_free_hand = 0
                freehand_bookings = sum(1 for row in booking_results if row[4] == 1)  # is_free_hand = 1
                
                print(f"总booking数: {total_bookings}")
                print(f"FCL booking数 (service_mode=2): {fcl_bookings}")
                print(f"LCL booking数 (service_mode=1,3): {lcl_bookings}")
                print(f"指定货booking数 (is_free_hand=0): {nominated_bookings}")
                print(f"自揽货booking数 (is_free_hand=1): {freehand_bookings}")
                
                print()
                print("期望值对比:")
                print(f"  bk_count期望=3, 实际={total_bookings} {'✓' if total_bookings == 3 else '✗'}")
                print(f"  nomi_count期望=2, 实际={nominated_bookings} {'✓' if nominated_bookings == 2 else '✗'}")
                
            # 查询TEU数据
            print("\n" + "=" * 60)
            print("TEU数据分析:")
            print("=" * 60)
            
            teu_query = """
            SELECT 
                seb.id as booking_id,
                seb.booking_no,
                sebc.quantity,
                cs.name as container_size,
                CASE
                    WHEN cs.name LIKE '%20%' THEN sebc.quantity
                    WHEN cs.name LIKE '%40%' THEN sebc.quantity * 2
                    WHEN cs.name LIKE '%45%' THEN sebc.quantity * 2
                    ELSE 0
                END as teu_value
            FROM sea_export_booking seb
            LEFT JOIN sea_export_bk_container sebc ON seb.id = sebc.bk_id
            LEFT JOIN container_size cs ON sebc.size_id = cs.id
            WHERE seb.job_file_id = 71973
            AND sebc.quantity > 0
            ORDER BY seb.id, sebc.id
            """
            
            cursor.execute(teu_query)
            teu_results = cursor.fetchall()
            
            print(f"找到 {len(teu_results)} 条容器记录:")
            
            total_teu = 0
            if teu_results:
                teu_columns = [desc[0] for desc in cursor.description]
                
                for i, row in enumerate(teu_results):
                    print(f"Container {i+1}:")
                    for j, col in enumerate(teu_columns):
                        print(f"  {col}: {row[j]}")
                    total_teu += row[4] if row[4] else 0
                    print()
                
                print(f"总TEU: {total_teu}")
                print(f"TEU期望=4, 实际={total_teu} {'✓' if total_teu == 4 else '✗'}")
            
            # 查询BL数据
            print("\n" + "=" * 60)
            print("BL数据分析:")
            print("=" * 60)
            
            bl_query = """
            SELECT 
                seb.id as booking_id,
                seb.booking_no,
                sebb.bl_id,
                sebl.bl_no,
                sebl.is_valid as bl_is_valid
            FROM sea_export_booking seb
            LEFT JOIN sea_export_bk_bl sebb ON seb.id = sebb.bk_id
            LEFT JOIN sea_export_bl sebl ON sebb.bl_id = sebl.id
            WHERE seb.job_file_id = 71973
            AND seb.is_valid = 1
            AND (sebl.is_valid = 1 OR sebl.is_valid IS NULL)
            ORDER BY seb.id, sebb.bl_id
            """
            
            cursor.execute(bl_query)
            bl_results = cursor.fetchall()
            
            print(f"找到 {len(bl_results)} 条BL记录:")
            
            if bl_results:
                bl_columns = [desc[0] for desc in cursor.description]
                
                unique_bl_ids = set()
                for i, row in enumerate(bl_results):
                    print(f"BL Record {i+1}:")
                    for j, col in enumerate(bl_columns):
                        print(f"  {col}: {row[j]}")
                    if row[2]:  # bl_id
                        unique_bl_ids.add(row[2])
                    print()
                
                bl_count = len(unique_bl_ids)
                print(f"唯一BL数量: {bl_count}")
                print(f"BL数量期望=3, 实际={bl_count} {'✓' if bl_count == 3 else '✗'}")
            
            cursor.close()
            
    except Exception as e:
        print(f"查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_job_71973_booking()
