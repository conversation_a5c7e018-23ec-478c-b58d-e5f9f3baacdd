#!/usr/bin/env python3
"""
测试调度器修复 - 使用强制执行模式
"""

import asyncio
import os
from datetime import datetime, date
from utils.app.profit_data_scheduler_pg import ProfitDataScheduler
from utils.basic.logger_config import setup_logger

# 设置日志
logger = setup_logger(
    name=__name__,
    level="info",
    log_to_console=True,
    log_to_file=True
)

async def test_scheduler_fix():
    """测试调度器修复"""
    
    # 设置环境变量
    os.environ['PRO2_SYSTEM_ID_QD'] = '86532'
    os.environ['LOCATION'] = 'QD'
    os.environ['FORCE_IMMEDIATE_EXECUTION'] = 'true'  # 强制立即执行
    
    logger.info("开始测试调度器修复...")
    
    try:
        scheduler = ProfitDataScheduler()
        
        # 确保表存在
        await scheduler.ensure_required_tables_exist()
        
        # 获取分析周期 - 使用当前日期以包含2025年4月
        periods = scheduler.get_analysis_periods(reference_date=date.today())

        # 找到2025年4月的周期
        target_period = None
        for period in periods:
            if period.start_date.year == 2025 and period.start_date.month == 4:
                target_period = period
                break

        if not target_period:
            logger.error("未找到2025年4月的分析周期")
            logger.info("可用的周期:")
            for i, period in enumerate(periods[-10:]):  # 显示最后10个周期
                logger.info(f"  {i+1}: {period.start_date} 到 {period.end_date}")
            return
        
        logger.info(f"找到目标周期: {target_period.start_date} 到 {target_period.end_date}")
        
        # 执行分析
        current_dt = datetime.now()
        
        # 检查是否需要运行分析
        should_run = await scheduler.should_run_analysis(current_dt, target_period.period_type)
        if not should_run:
            logger.warning("当前时间不在分析窗口内，但强制执行模式已启用")
        
        # 检查是否需要重新检查
        needs_check = await scheduler.needs_check(target_period, current_dt)
        logger.info(f"是否需要检查: {needs_check}")
        
        # 获取会话ID
        session_id = await scheduler.get_session_id()
        analysis_timestamp = int(current_dt.timestamp())
        
        logger.info(f"开始分析周期: {target_period.start_date} 到 {target_period.end_date}")
        logger.info(f"会话ID: {session_id}")

        # 获取Job数据
        from utils.database.db_pro2_basic import get_job_details_with_transhipment

        logger.info("获取Job数据...")
        job_result = await get_job_details_with_transhipment(
            target_period.start_date.strftime('%Y-%m-%d'),
            target_period.end_date.strftime('%Y-%m-%d')
        )
        
        if not job_result or not job_result.get('data'):
            logger.warning("未获取到Job数据")
            return
        
        job_data = job_result['data']
        logger.info(f"获取到 {len(job_data)} 条Job记录")
        
        # 查找job_file_id=71973的记录
        target_job = None
        for record in job_data:
            if record.get('job_file_id') == 71973:
                target_job = record
                break
        
        if target_job:
            logger.info("=" * 60)
            logger.info("找到job_file_id=71973的记录:")
            logger.info("=" * 60)
            
            # 显示关键字段
            key_fields = [
                'job_file_id', 'job_file_no', 'business_type_name', 'job_date',
                'bk_count', 'bl_count', 'total_teu', 'all_nominated_count'
            ]
            
            for field in key_fields:
                value = target_job.get(field, 'N/A')
                logger.info(f"  {field}: {value}")
            
            # 验证期望值
            expected = {
                'bk_count': 3,
                'total_teu': 4,
                'bl_count': 3,
                'all_nominated_count': 2
            }
            
            logger.info("=" * 60)
            logger.info("数据验证:")
            logger.info("=" * 60)
            
            all_correct = True
            for field, expected_value in expected.items():
                actual_value = target_job.get(field)
                if actual_value is not None:
                    status = "✓" if actual_value == expected_value else "✗"
                    if actual_value != expected_value:
                        all_correct = False
                    logger.info(f"  {field}: 期望={expected_value}, 实际={actual_value} {status}")
                else:
                    logger.info(f"  {field}: 期望={expected_value}, 实际=未找到字段 ✗")
                    all_correct = False
            
            logger.info(f"\n从firebird提取的数据验证: {'✓ 正确' if all_correct else '✗ 错误'}")
            
            # 保存数据到PostgreSQL
            logger.info("保存Job数据到PostgreSQL...")
            await scheduler.save_job_details([target_job], session_id, analysis_timestamp)
            logger.info("Job数据保存完成")
            
        else:
            logger.warning("未找到job_file_id=71973的记录")
            # 显示前几条记录的job_file_id以供参考
            logger.info("前10条记录的job_file_id:")
            for i, record in enumerate(job_data[:10]):
                logger.info(f"  {i+1}: job_file_id={record.get('job_file_id')}, job_file_no={record.get('job_file_no')}")
        
        logger.info("测试完成")
        
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(test_scheduler_fix())
