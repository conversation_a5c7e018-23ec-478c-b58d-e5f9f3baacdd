#!/usr/bin/env python3
"""
测试job_file_id=71973的数据提取和分析
"""

import asyncio
import os
from datetime import datetime, date
from utils.database.db_pro2_sea_air_profit import query_job_details_with_statistics_by_date
from utils.basic.logger_config import setup_logger

# 设置日志
logger = setup_logger(
    name=__name__,
    level="info",
    log_to_console=True,
    log_to_file=True
)

async def test_job_71973():
    """测试job_file_id=71973的数据"""

    # 设置环境变量
    os.environ['PRO2_SYSTEM_ID_QD'] = '86532'
    os.environ['LOCATION'] = 'QD'

    logger.info("开始测试job_file_id=71973的数据提取...")

    # 直接调用底层函数查询2024年8月的数据
    result = await asyncio.to_thread(
        query_job_details_with_statistics_by_date,
        '2024-08-01', '2024-08-31',
        logger_prefix="[TEST]"
    )

    if not result:
        logger.error("未获取到任何数据")
        return

    data = result
    logger.info(f"总共获取到 {len(data)} 条记录")
    
    # 查找job_file_id=71973的记录
    target_job = None
    for record in data:
        if record.get('job_file_id') == 71973 or record.get('job_id') == 71973:
            target_job = record
            break
    
    if not target_job:
        logger.error("未找到job_file_id=71973的记录")
        # 显示前几条记录的job_file_id以供参考
        logger.info("前10条记录的job_file_id:")
        for i, record in enumerate(data[:10]):
            logger.info(f"  {i+1}: job_file_id={record.get('job_file_id')}, job_file_no={record.get('job_file_no')}")
        return
    
    logger.info("=" * 60)
    logger.info(f"找到job_file_id=71973的记录:")
    logger.info("=" * 60)
    
    # 显示关键字段
    key_fields = [
        'job_file_id', 'job_file_no', 'business_type_name', 'job_date',
        'bk_count', 'bl_count', 'bill_count', 'total_teu', 'teu', 
        'all_nominated_count', 'nomi_count', 'total_rt', 'rt',
        'vessel', 'voyage', 'pol_code', 'pod_code'
    ]
    
    for field in key_fields:
        value = target_job.get(field, 'N/A')
        logger.info(f"  {field}: {value}")
    
    logger.info("=" * 60)
    logger.info("完整记录内容:")
    logger.info("=" * 60)
    
    # 显示所有字段
    for key, value in sorted(target_job.items()):
        logger.info(f"  {key}: {value}")
    
    # 验证期望值
    expected = {
        'bk_count': 3,
        'teu': 4,
        'bill_count': 3,
        'nomi_count': 2
    }
    
    logger.info("=" * 60)
    logger.info("数据验证:")
    logger.info("=" * 60)
    
    for field, expected_value in expected.items():
        # 尝试多个可能的字段名
        possible_fields = []
        if field == 'bk_count':
            possible_fields = ['bk_count', '订舱数']
        elif field == 'teu':
            possible_fields = ['teu', 'total_teu', 'TEU总计']
        elif field == 'bill_count':
            possible_fields = ['bill_count', 'bl_count', '提单数量']
        elif field == 'nomi_count':
            possible_fields = ['nomi_count', 'all_nominated_count', '指定货票数']
        
        actual_value = None
        found_field = None
        for pf in possible_fields:
            if pf in target_job:
                actual_value = target_job[pf]
                found_field = pf
                break
        
        if actual_value is not None:
            status = "✓" if actual_value == expected_value else "✗"
            logger.info(f"  {field} ({found_field}): 期望={expected_value}, 实际={actual_value} {status}")
        else:
            logger.info(f"  {field}: 期望={expected_value}, 实际=未找到字段 ✗")

if __name__ == "__main__":
    asyncio.run(test_job_71973())
