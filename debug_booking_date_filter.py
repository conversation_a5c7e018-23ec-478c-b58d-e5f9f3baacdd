#!/usr/bin/env python3
"""
调试booking数据的日期过滤问题
"""

import os
from utils.basic.fb_conn import get_pooled_pro2_connection

def debug_booking_date_filter():
    """调试booking数据的日期过滤问题"""
    
    print("=" * 80)
    print("调试booking数据的日期过滤问题 - job_file_id=71973")
    print("=" * 80)
    
    try:
        with get_pooled_pro2_connection() as conn:
            cursor = conn.cursor()
            
            # 1. 查询job_file_id=71973的job_date
            print("1. Job基本信息:")
            print("-" * 40)
            
            job_query = """
            SELECT 
                jf.id as job_file_id,
                jf.job_file_no,
                icn.job_date,
                jf.is_active,
                icn.is_valid
            FROM job_file jf
            LEFT JOIN invoice_cost_note icn ON jf.id = icn.job_file_id
            WHERE jf.id = 71973
            """
            
            cursor.execute(job_query)
            job_results = cursor.fetchall()
            
            print(f"找到 {len(job_results)} 条job记录:")
            
            if job_results:
                job_columns = [desc[0] for desc in cursor.description]
                
                for i, row in enumerate(job_results):
                    print(f"\nJob {i+1}:")
                    for j, col in enumerate(job_columns):
                        print(f"  {col}: {row[j]}")
            
            # 2. 查询job_file_id=71973的所有booking记录（不带日期过滤）
            print("\n" + "=" * 40)
            print("2. 所有booking记录（无日期过滤）:")
            print("-" * 40)
            
            booking_no_filter_query = """
            SELECT 
                seb.id as booking_id,
                seb.job_file_id,
                seb.booking_no,
                seb.service_mode,
                seb.is_free_hand,
                seb.is_valid,
                jf.job_file_no,
                icn.job_date
            FROM sea_export_booking seb
            LEFT JOIN sea_export_job_file sejf ON seb.job_file_id = sejf.job_file_id
            LEFT JOIN invoice_cost_note icn ON sejf.job_file_id = icn.job_file_id
            LEFT JOIN job_file jf ON sejf.job_file_id = jf.id
            WHERE seb.job_file_id = 71973
            AND seb.is_valid = 1
            ORDER BY seb.id
            """
            
            cursor.execute(booking_no_filter_query)
            booking_no_filter_results = cursor.fetchall()
            
            print(f"找到 {len(booking_no_filter_results)} 条booking记录:")
            
            if booking_no_filter_results:
                booking_columns = [desc[0] for desc in cursor.description]
                
                for i, row in enumerate(booking_no_filter_results):
                    print(f"\nBooking {i+1}:")
                    for j, col in enumerate(booking_columns):
                        print(f"  {col}: {row[j]}")
            
            # 3. 查询job_file_id=71973的booking记录（带2025年4月日期过滤）
            print("\n" + "=" * 40)
            print("3. 2025年4月日期过滤的booking记录:")
            print("-" * 40)
            
            booking_with_filter_query = """
            SELECT 
                seb.id as booking_id,
                seb.job_file_id,
                seb.booking_no,
                seb.service_mode,
                seb.is_free_hand,
                seb.is_valid,
                jf.job_file_no,
                icn.job_date
            FROM sea_export_booking seb
            LEFT JOIN sea_export_job_file sejf ON seb.job_file_id = sejf.job_file_id
            LEFT JOIN invoice_cost_note icn ON sejf.job_file_id = icn.job_file_id
            LEFT JOIN job_file jf ON sejf.job_file_id = jf.id
            WHERE seb.job_file_id = 71973
            AND seb.is_valid = 1
            AND icn.job_date BETWEEN '2025-04-01' AND '2025-04-30'
            ORDER BY seb.id
            """
            
            cursor.execute(booking_with_filter_query)
            booking_with_filter_results = cursor.fetchall()
            
            print(f"找到 {len(booking_with_filter_results)} 条booking记录:")
            
            if booking_with_filter_results:
                for i, row in enumerate(booking_with_filter_results):
                    print(f"\nBooking {i+1}:")
                    for j, col in enumerate(booking_columns):
                        print(f"  {col}: {row[j]}")
            
            # 4. 对比分析
            print("\n" + "=" * 40)
            print("4. 对比分析:")
            print("-" * 40)
            
            print(f"无日期过滤的booking数量: {len(booking_no_filter_results)}")
            print(f"2025年4月日期过滤的booking数量: {len(booking_with_filter_results)}")
            
            if len(booking_no_filter_results) != len(booking_with_filter_results):
                print("⚠️  发现问题：日期过滤导致booking记录数量不一致！")
                print("这可能是导致统计错误的原因。")
                
                # 找出被过滤掉的记录
                no_filter_ids = set(row[0] for row in booking_no_filter_results)
                with_filter_ids = set(row[0] for row in booking_with_filter_results)
                filtered_out_ids = no_filter_ids - with_filter_ids
                
                if filtered_out_ids:
                    print(f"\n被过滤掉的booking ID: {filtered_out_ids}")
                    
                    # 显示被过滤掉的记录的详细信息
                    for booking_id in filtered_out_ids:
                        for row in booking_no_filter_results:
                            if row[0] == booking_id:
                                print(f"\n被过滤的booking {booking_id}:")
                                for j, col in enumerate(booking_columns):
                                    print(f"  {col}: {row[j]}")
                                break
            else:
                print("✓ 日期过滤没有影响booking记录数量")
            
            cursor.close()
            
    except Exception as e:
        print(f"查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 设置环境变量
    os.environ['PRO2_SYSTEM_ID_QD'] = '86532'
    os.environ['LOCATION'] = 'QD'
    
    debug_booking_date_filter()
