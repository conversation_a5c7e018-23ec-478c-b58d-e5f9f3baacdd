#!/usr/bin/env python3
"""
测试字段映射修复
"""

import os
from utils.app.profit_data_scheduler_pg import ProfitDataScheduler

def test_field_mapping():
    """测试字段映射逻辑"""
    
    # 设置环境变量
    os.environ['PRO2_SYSTEM_ID_QD'] = '86532'
    os.environ['LOCATION'] = 'QD'
    os.environ['PG_HOST'] = 'localhost'
    os.environ['PG_USER'] = 'test'
    os.environ['PG_PASSWORD'] = 'test'
    os.environ['PG_DB_CMSDATA'] = 'test'
    
    scheduler = ProfitDataScheduler()
    
    # 模拟从firebird提取的数据（基于mcp-cms项目的字段名）
    test_data = {
        'job_file_id': 71973,
        'job_file_no': 'QD24080001',
        'business_type_name': '海运出口',
        'job_date': '2024-08-01',
        'vessel': 'TEST VESSEL',
        'voyage': 'V001',
        'pol_code': 'CNQIN',
        'pod_code': 'USNYC',
        
        # 关键字段 - 使用mcp-cms项目中的字段名
        'bk_count': 3,           # 订舱数
        'bl_count': 3,           # 提单数量 
        'total_teu': 4,          # TEU总计
        'all_nominated_count': 2, # 指定货票数
        'all_nominated_rt': 1500, # 指定货RT总和
        'total_rt': 2000,        # RT总计
        
        'income': 10000,
        'cost': 8000,
        'profit': 2000,
        'operator_name': '测试操作员',
        'job_handling_agent': '测试代理',
        'is_consol': 0,
        'consol_20_count': 0,
        'consol_40_count': 0,
        'operator_dept_name': '操作部',
        'is_op_finished': 1,
        'is_checked': 1,
        'job_id': 71973,
        'type_id': 1,
        'operator_id': 123
    }
    
    print("=" * 60)
    print("测试字段映射修复")
    print("=" * 60)
    
    print("原始数据:")
    key_fields = ['bk_count', 'bl_count', 'total_teu', 'all_nominated_count']
    for field in key_fields:
        print(f"  {field}: {test_data.get(field)}")
    
    print("\n字段映射测试:")
    
    # 测试bk_count映射
    bk_count = int(scheduler.safe_int_convert(test_data.get('bk_count') or test_data.get('订舱数'), 0))
    print(f"  bk_count: {bk_count} (期望: 3)")
    
    # 测试teu映射
    def safe_numeric_convert(value, default=0, decimal_places=None):
        if value is None or value == '':
            return default
        try:
            float_val = float(value) if value != 0 else default
            if decimal_places is not None:
                float_val = round(float_val, decimal_places)
            return float_val
        except (ValueError, TypeError):
            return default
    
    teu = safe_numeric_convert(test_data.get('total_teu') or test_data.get('teu_sum') or test_data.get('teu') or test_data.get('TEU总计'), 0, 3)
    print(f"  teu: {teu} (期望: 4)")
    
    # 测试bill_count映射
    bill_count = int(safe_numeric_convert(test_data.get('bl_count') or test_data.get('bill_count') or test_data.get('提单数量'), 0))
    print(f"  bill_count: {bill_count} (期望: 3)")
    
    # 测试nomi_count映射
    nomi_count = int(safe_numeric_convert(test_data.get('all_nominated_count') or test_data.get('nomi_count') or test_data.get('指定货票数'), 0))
    print(f"  nomi_count: {nomi_count} (期望: 2)")
    
    print("\n验证结果:")
    expected = {'bk_count': 3, 'teu': 4, 'bill_count': 3, 'nomi_count': 2}
    actual = {'bk_count': bk_count, 'teu': teu, 'bill_count': bill_count, 'nomi_count': nomi_count}
    
    all_correct = True
    for field, expected_value in expected.items():
        actual_value = actual[field]
        status = "✓" if actual_value == expected_value else "✗"
        if actual_value != expected_value:
            all_correct = False
        print(f"  {field}: 期望={expected_value}, 实际={actual_value} {status}")
    
    print(f"\n总体结果: {'✓ 所有字段映射正确' if all_correct else '✗ 存在字段映射错误'}")
    
    return all_correct

if __name__ == "__main__":
    test_field_mapping()
