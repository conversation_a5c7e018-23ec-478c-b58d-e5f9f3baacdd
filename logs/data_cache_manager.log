2025-07-10 23:10:36 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:11:23 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:12:16 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:16:27 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:21:04 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:22:15 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:22:39 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:23:26 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:24:32 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:25:40 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:26:52 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:27:43 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:47:25 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:49:01 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:52:04 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:53:00 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-10 23:58:34 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-11 00:02:24 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-11 00:03:22 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-11 00:04:44 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-11 00:06:03 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-11 00:07:03 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-11 00:10:59 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-11 00:12:16 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-11 00:20:31 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-13 23:15:22 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-13 23:18:00 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-13 23:23:00 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-13 23:25:04 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=10000条
2025-07-13 23:27:10 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=10000条
2025-07-13 23:32:52 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-13 23:56:40 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-13 23:57:14 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-13 23:57:37 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:00:10 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:06:30 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:07:02 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:11:04 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:12:29 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:12:52 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:15:48 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:17:36 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:18:24 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:18:41 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:25:06 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:25:30 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:28:25 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:28:51 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:38:48 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 00:49:30 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 01:00:34 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 01:06:47 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 01:07:25 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 01:11:26 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 01:13:12 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 01:16:54 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 01:17:23 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 01:19:10 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 01:19:34 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 01:25:43 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 10:40:21 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 10:40:48 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 10:40:48 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_from_tokens_table_cached__begin_date=2025-01-01_month_count=6_pro2_system_id=86021
2025-07-14 10:40:48 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_from_tokens_table_cached 获取数据
2025-07-14 10:41:24 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 10:41:24 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 10:41:24 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_from_tokens_table_cached 获取数据
2025-07-14 10:51:35 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 10:51:35 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 10:51:35 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_from_tokens_table_cached 获取数据
2025-07-14 10:51:50 - utils.basic.data_cache_manager - INFO - 缓存数据: get_job_details_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021, 记录数: 6791
2025-07-14 10:51:50 - utils.basic.data_cache_manager - INFO - 异步函数 get_job_details_from_tokens_table_cached 执行完成，耗时: 15.15 秒
2025-07-14 10:53:34 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 10:53:34 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 10:53:34 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_from_tokens_table_cached 获取数据
2025-07-14 10:53:36 - utils.basic.data_cache_manager - INFO - 缓存数据: get_job_details_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021, 记录数: 6791
2025-07-14 10:53:36 - utils.basic.data_cache_manager - INFO - 异步函数 get_job_details_from_tokens_table_cached 执行完成，耗时: 2.52 秒
2025-07-14 10:56:20 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 10:56:20 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 10:56:20 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_from_tokens_table_cached 获取数据
2025-07-14 10:56:26 - utils.basic.data_cache_manager - INFO - 缓存数据: get_job_details_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021, 记录数: 6791
2025-07-14 10:56:26 - utils.basic.data_cache_manager - INFO - 异步函数 get_job_details_from_tokens_table_cached 执行完成，耗时: 5.81 秒
2025-07-14 11:01:05 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 11:01:05 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 11:01:05 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_from_tokens_table_cached 获取数据
2025-07-14 11:01:07 - utils.basic.data_cache_manager - INFO - 缓存数据: get_job_details_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021, 记录数: 6791
2025-07-14 11:01:07 - utils.basic.data_cache_manager - INFO - 异步函数 get_job_details_from_tokens_table_cached 执行完成，耗时: 1.89 秒
2025-07-14 11:14:30 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 12:22:28 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 13:10:49 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 13:13:31 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 13:14:00 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 13:14:00 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-01-31_pro2_system_id=86021
2025-07-14 13:14:00 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 13:14:02 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-01-31_pro2_system_id=86021, 记录数: 0
2025-07-14 13:14:02 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 2.05 秒
2025-07-14 13:16:21 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 13:16:21 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 13:16:21 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 13:16:26 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021, 记录数: 0
2025-07-14 13:16:26 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 4.83 秒
2025-07-14 13:16:56 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 13:16:56 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 13:16:56 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 13:17:01 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021, 记录数: 0
2025-07-14 13:17:01 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 5.05 秒
2025-07-14 13:24:38 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 13:24:41 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached_2025-01-01_2025-01-31
2025-07-14 13:24:41 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 13:24:41 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached_2025-01-01_2025-01-31, 记录数: 0
2025-07-14 13:24:41 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 0.38 秒
2025-07-14 13:30:21 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 13:30:21 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-01-31_pro2_system_id=86021
2025-07-14 13:30:21 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 13:30:34 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-01-31_pro2_system_id=86021, 记录数: 0
2025-07-14 13:30:34 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 12.72 秒
2025-07-14 13:33:18 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 13:33:18 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-01-31_pro2_system_id=86021
2025-07-14 13:33:18 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 13:33:24 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-01-31_pro2_system_id=86021, 记录数: 0
2025-07-14 13:33:24 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 5.24 秒
2025-07-14 13:36:23 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 13:36:58 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached_2025-01-01_2025-01-31
2025-07-14 13:36:58 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 13:36:59 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached_2025-01-01_2025-01-31, 记录数: 0
2025-07-14 13:36:59 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 0.84 秒
2025-07-14 13:37:01 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_from_tokens_table_cached_2025-01-01_2025-01-31
2025-07-14 13:37:01 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_from_tokens_table_cached 获取数据
2025-07-14 13:37:01 - utils.basic.data_cache_manager - INFO - 缓存数据: get_job_details_from_tokens_table_cached_2025-01-01_2025-01-31, 记录数: 0
2025-07-14 13:37:01 - utils.basic.data_cache_manager - INFO - 异步函数 get_job_details_from_tokens_table_cached 执行完成，耗时: 0.11 秒
2025-07-14 13:39:37 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 13:39:39 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_from_tokens_table_cached_2025-01-01_2025-01-31
2025-07-14 13:39:39 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_from_tokens_table_cached 获取数据
2025-07-14 13:39:39 - utils.basic.data_cache_manager - INFO - 缓存数据: get_job_details_from_tokens_table_cached_2025-01-01_2025-01-31, 记录数: 0
2025-07-14 13:39:39 - utils.basic.data_cache_manager - INFO - 异步函数 get_job_details_from_tokens_table_cached 执行完成，耗时: 0.72 秒
2025-07-14 13:42:06 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 13:42:06 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-01-31_pro2_system_id=86021
2025-07-14 13:42:06 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 13:42:09 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-01-31_pro2_system_id=86021, 记录数: 0
2025-07-14 13:42:09 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 2.88 秒
2025-07-14 13:52:10 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 13:52:31 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 14:06:12 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 14:06:47 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 14:07:46 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 14:15:11 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 14:31:06 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 14:31:19 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 14:45:32 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 15:37:21 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 15:42:02 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:00:25 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:00:25 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_month_count=6_pro2_system_id=86021
2025-07-14 16:00:25 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 16:00:42 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:00:43 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 16:00:43 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 16:00:57 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021, 记录数: 0
2025-07-14 16:00:57 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 14.22 秒
2025-07-14 16:07:18 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:07:18 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 16:07:18 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 16:07:32 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021, 记录数: 0
2025-07-14 16:07:32 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 13.53 秒
2025-07-14 16:12:04 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:15:49 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:22:57 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:22:57 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 16:22:57 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 16:23:00 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021, 记录数: 0
2025-07-14 16:23:00 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 2.95 秒
2025-07-14 16:33:39 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:33:39 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 16:33:39 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 16:33:49 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021, 记录数: 0
2025-07-14 16:33:49 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 9.63 秒
2025-07-14 16:38:21 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:38:21 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 16:38:21 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 16:38:23 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021, 记录数: 0
2025-07-14 16:38:23 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 2.43 秒
2025-07-14 16:40:16 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:44:08 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:47:33 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:54:20 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:55:11 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:55:11 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 16:55:11 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 16:55:14 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021, 记录数: 0
2025-07-14 16:55:14 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 2.50 秒
2025-07-14 16:58:42 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 16:58:42 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 16:58:42 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 16:58:48 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021, 记录数: 0
2025-07-14 16:58:48 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 5.59 秒
2025-07-14 17:00:09 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 17:02:43 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 17:05:21 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 17:07:22 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 17:08:48 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 17:10:39 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 17:13:29 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 17:15:41 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 17:15:41 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021
2025-07-14 17:15:41 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 17:15:44 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2025-01-01_end_date=2025-06-30_pro2_system_id=86021, 记录数: 0
2025-07-14 17:15:44 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 3.12 秒
2025-07-14 20:58:59 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 21:02:48 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 21:04:03 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 21:23:30 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 21:27:07 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 21:28:21 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 21:28:40 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 21:46:59 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 21:47:41 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 21:48:01 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 21:54:11 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 21:54:11 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_from_tokens_table_cached__begin_date=2020-01-01_end_date=2025-06-30_pro2_system_id=86532
2025-07-14 21:54:11 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_from_tokens_table_cached 获取数据
2025-07-14 21:54:32 - utils.basic.data_cache_manager - INFO - 缓存数据: get_job_details_from_tokens_table_cached__begin_date=2020-01-01_end_date=2025-06-30_pro2_system_id=86532, 记录数: 0
2025-07-14 21:54:32 - utils.basic.data_cache_manager - INFO - 异步函数 get_job_details_from_tokens_table_cached 执行完成，耗时: 20.15 秒
2025-07-14 22:30:56 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 23:05:18 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 23:05:18 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2020-01-01_end_date=2025-06-30_pro2_system_id=86532
2025-07-14 23:05:18 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 23:05:51 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2020-01-01_end_date=2025-06-30_pro2_system_id=86532, 记录数: 0
2025-07-14 23:05:51 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 33.23 秒
2025-07-14 23:07:29 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-14 23:07:29 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2022-01-01_end_date=2025-06-30_pro2_system_id=86532
2025-07-14 23:07:29 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-14 23:07:53 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2022-01-01_end_date=2025-06-30_pro2_system_id=86532, 记录数: 0
2025-07-14 23:07:53 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 24.16 秒
2025-07-16 03:58:47 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-16 04:02:55 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-16 04:03:02 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_with_transhipment_2025-05-01_2025-05-31
2025-07-16 04:03:02 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_with_transhipment 获取数据
2025-07-16 04:03:02 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_with_transhipment_2025-06-01_2025-06-30
2025-07-16 04:03:02 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_with_transhipment 获取数据
2025-07-16 04:09:07 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-16 04:09:12 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_with_transhipment_2025-06-01_2025-07-16
2025-07-16 04:09:12 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_with_transhipment 获取数据
2025-07-16 04:09:42 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_with_transhipment_2025-06-01_2025-07-16, 记录数: 1831
2025-07-16 04:09:42 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_with_transhipment 执行完成，耗时: 30.19 秒
2025-07-16 04:39:50 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-16 04:47:36 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-16 04:47:36 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_with_transhipment_2025-06-01_2025-06-05
2025-07-16 04:47:36 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_with_transhipment 获取数据
2025-07-16 04:48:00 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_with_transhipment_2025-06-01_2025-06-05, 记录数: 172
2025-07-16 04:48:00 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_with_transhipment 执行完成，耗时: 23.42 秒
2025-07-16 04:54:06 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-16 04:54:06 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_with_transhipment_2025-06-01_2025-06-05
2025-07-16 04:54:06 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_with_transhipment 获取数据
2025-07-16 04:54:30 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_with_transhipment_2025-06-01_2025-06-05, 记录数: 172
2025-07-16 04:54:30 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_with_transhipment 执行完成，耗时: 23.41 秒
2025-07-17 22:35:26 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-17 22:36:10 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-17 22:40:40 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-17 22:41:10 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-17 22:46:45 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-17 22:47:16 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-17 22:48:10 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-17 23:15:20 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-17 23:17:31 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-17 23:17:33 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_with_transhipment_2024-01-01_2024-01-01
2025-07-17 23:17:33 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_with_transhipment 获取数据
2025-07-17 23:18:31 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_with_transhipment_2024-01-01_2024-01-01, 记录数: 15
2025-07-17 23:18:31 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_with_transhipment 执行完成，耗时: 58.12 秒
2025-07-17 23:18:31 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_with_transhipment_2024-01-01_2024-01-07
2025-07-17 23:18:31 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_with_transhipment 获取数据
2025-07-17 23:19:31 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_with_transhipment_2024-01-01_2024-01-07, 记录数: 569
2025-07-17 23:19:31 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_with_transhipment 执行完成，耗时: 59.77 秒
2025-07-17 23:35:31 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-17 23:36:23 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-17 23:36:23 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_with_transhipment_2025-07-17_2025-07-17
2025-07-17 23:36:23 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_with_transhipment 获取数据
2025-07-17 23:36:57 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_with_transhipment_2025-07-17_2025-07-17, 记录数: 60
2025-07-17 23:36:57 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_with_transhipment 执行完成，耗时: 34.03 秒
2025-07-17 23:36:57 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_with_transhipment_2025-07-07_2025-07-17
2025-07-17 23:36:57 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_with_transhipment 获取数据
2025-07-17 23:37:41 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_with_transhipment_2025-07-07_2025-07-17, 记录数: 934
2025-07-17 23:37:41 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_with_transhipment 执行完成，耗时: 43.34 秒
2025-07-17 23:38:07 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-17 23:38:07 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_with_transhipment_2025-07-17_2025-07-17
2025-07-17 23:38:07 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_with_transhipment 获取数据
2025-07-17 23:38:40 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_with_transhipment_2025-07-17_2025-07-17, 记录数: 60
2025-07-17 23:38:40 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_with_transhipment 执行完成，耗时: 32.86 秒
2025-07-17 23:38:40 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_with_transhipment_2025-07-07_2025-07-17
2025-07-17 23:38:40 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_with_transhipment 获取数据
2025-07-17 23:39:24 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_with_transhipment_2025-07-07_2025-07-17, 记录数: 934
2025-07-17 23:39:24 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_with_transhipment 执行完成，耗时: 43.75 秒
2025-07-17 23:39:55 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-17 23:39:55 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_with_transhipment_2025-07-17_2025-07-17
2025-07-17 23:39:55 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_with_transhipment 获取数据
2025-07-17 23:40:28 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_with_transhipment_2025-07-17_2025-07-17, 记录数: 60
2025-07-17 23:40:28 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_with_transhipment 执行完成，耗时: 32.49 秒
2025-07-17 23:40:28 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_with_transhipment_2025-07-07_2025-07-17
2025-07-17 23:40:28 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_with_transhipment 获取数据
2025-07-17 23:41:10 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_with_transhipment_2025-07-07_2025-07-17, 记录数: 934
2025-07-17 23:41:10 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_with_transhipment 执行完成，耗时: 42.03 秒
2025-07-17 23:56:32 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-17 23:56:33 - utils.basic.data_cache_manager - INFO - 清空了所有缓存: 0 个条目
2025-07-17 23:56:33 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_with_transhipment_2025-07-16_2025-07-17
2025-07-17 23:56:33 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_with_transhipment 获取数据
2025-07-17 23:57:10 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_with_transhipment_2025-07-16_2025-07-17, 记录数: 130
2025-07-17 23:57:10 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_with_transhipment 执行完成，耗时: 37.39 秒
2025-07-20 13:39:02 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-20 13:39:02 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2022-01-01_end_date=2025-06-30_pro2_system_id=86532
2025-07-20 13:39:02 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-20 13:39:37 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-20 13:39:37 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_from_tokens_table_cached__begin_date=2022-01-01_end_date=2025-06-30
2025-07-20 13:39:37 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_from_tokens_table_cached 获取数据
2025-07-20 13:40:53 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_from_tokens_table_cached__begin_date=2022-01-01_end_date=2025-06-30, 记录数: 0
2025-07-20 13:40:53 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_from_tokens_table_cached 执行完成，耗时: 76.60 秒
2025-07-20 13:43:06 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_from_tokens_table_cached__begin_date=2022-01-01_end_date=2025-06-30
2025-07-20 13:43:06 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_from_tokens_table_cached 获取数据
2025-07-20 13:43:27 - utils.basic.data_cache_manager - INFO - 缓存数据: get_job_details_from_tokens_table_cached__begin_date=2022-01-01_end_date=2025-06-30, 记录数: 0
2025-07-20 13:43:27 - utils.basic.data_cache_manager - INFO - 异步函数 get_job_details_from_tokens_table_cached 执行完成，耗时: 20.69 秒
2025-07-26 23:34:24 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-26 23:37:19 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-26 23:37:57 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-26 23:38:38 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-26 23:39:38 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-26 23:48:29 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-26 23:52:29 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-26 23:52:56 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-26 23:53:36 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-26 23:55:37 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-26 23:57:19 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-27 00:01:07 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-27 00:02:28 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-27 00:03:14 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
2025-07-28 23:33:49 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=60分钟, 最大缓存=1000条
