#!/usr/bin/env python3
"""
测试2025年4月的调度器修复
"""

import asyncio
import os
from datetime import datetime, date
from utils.app.profit_data_scheduler_pg import ProfitDataScheduler
from utils.basic.logger_config import setup_logger

# 设置日志
logger = setup_logger(
    name=__name__,
    level="info",
    log_to_console=True,
    log_to_file=True
)

async def test_scheduler_april_2025():
    """测试2025年4月的调度器"""
    
    # 设置环境变量
    os.environ['PRO2_SYSTEM_ID_QD'] = '86532'
    os.environ['LOCATION'] = 'QD'
    os.environ['FORCE_IMMEDIATE_EXECUTION'] = 'true'  # 强制立即执行
    
    logger.info("开始测试2025年4月的调度器...")
    
    try:
        scheduler = ProfitDataScheduler()
        
        # 确保表存在
        await scheduler.ensure_required_tables_exist()
        
        # 手动指定2025年4月的周期
        from utils.app.profit_data_scheduler_pg import AnalysisPeriod
        target_period = AnalysisPeriod(
            start_date=date(2025, 4, 1),
            end_date=date(2025, 4, 30),
            period_type='monthly',
            check_frequency='daily'
        )
        
        logger.info(f"测试周期: {target_period.start_date} 到 {target_period.end_date}")
        
        # 获取会话ID
        session_id = await scheduler.get_session_id()
        analysis_timestamp = int(datetime.now().timestamp())
        
        logger.info(f"会话ID: {session_id}")
        
        # 获取Job数据
        from utils.database.db_pro2_basic import get_job_details_with_transhipment
        
        logger.info("获取Job数据...")
        job_result = await get_job_details_with_transhipment(
            target_period.start_date.strftime('%Y-%m-%d'),
            target_period.end_date.strftime('%Y-%m-%d')
        )
        
        if not job_result or not job_result.get('data'):
            logger.warning("未获取到Job数据")
            return
        
        job_data = job_result['data']
        logger.info(f"获取到 {len(job_data)} 条Job记录")
        
        # 查找job_file_id=71973的记录
        target_job = None
        for record in job_data:
            if record.get('job_file_id') == 71973:
                target_job = record
                break
        
        if target_job:
            logger.info("=" * 60)
            logger.info("找到job_file_id=71973的记录:")
            logger.info("=" * 60)
            
            # 显示关键字段
            key_fields = [
                'job_file_id', 'job_file_no', 'business_type_name', 'job_date',
                'bk_count', 'bl_count', 'total_teu', 'all_nominated_count'
            ]
            
            for field in key_fields:
                value = target_job.get(field, 'N/A')
                logger.info(f"  {field}: {value}")
            
            # 验证期望值
            expected = {
                'bk_count': 3,
                'total_teu': 4,
                'bl_count': 3,
                'all_nominated_count': 2
            }
            
            logger.info("=" * 60)
            logger.info("数据验证:")
            logger.info("=" * 60)
            
            all_correct = True
            for field, expected_value in expected.items():
                actual_value = target_job.get(field)
                if actual_value is not None:
                    status = "✓" if actual_value == expected_value else "✗"
                    if actual_value != expected_value:
                        all_correct = False
                    logger.info(f"  {field}: 期望={expected_value}, 实际={actual_value} {status}")
                else:
                    logger.info(f"  {field}: 期望={expected_value}, 实际=未找到字段 ✗")
                    all_correct = False
            
            logger.info(f"\n从firebird提取的数据验证: {'✓ 正确' if all_correct else '✗ 错误'}")
            
            if all_correct:
                logger.info("数据提取正确，保存到PostgreSQL...")
                # 保存数据到PostgreSQL
                await scheduler.save_job_details([target_job], session_id, analysis_timestamp)
                logger.info("Job数据保存完成")
                
                # 验证保存到PostgreSQL的数据
                logger.info("验证PostgreSQL中的数据...")
                from utils.basic.pg_conn import get_postgres_connection_async
                
                async with get_postgres_connection_async() as pg_conn:
                    pg_result = await pg_conn.fetchrow("""
                        SELECT job_id, job_no, bk_count, bill_count, teu, nomi_count
                        FROM t_job_details
                        WHERE job_id = $1
                        ORDER BY analysis_timestamp DESC
                        LIMIT 1
                    """, 71973)

                if pg_result:
                            logger.info("PostgreSQL中的数据:")
                            logger.info(f"  job_file_id: {pg_result[0]}")
                            logger.info(f"  job_file_no: {pg_result[1]}")
                            logger.info(f"  bk_count: {pg_result[2]}")
                            logger.info(f"  bl_count: {pg_result[3]}")
                            logger.info(f"  teu: {pg_result[4]}")
                            logger.info(f"  nomi_count: {pg_result[5]}")
                            
                            # 验证PostgreSQL数据
                            pg_expected = {
                                'bk_count': 3,
                                'teu': 4,
                                'bl_count': 3,
                                'nomi_count': 2
                            }
                            
                            pg_actual = {
                                'bk_count': pg_result[2],
                                'teu': pg_result[4],
                                'bl_count': pg_result[3],
                                'nomi_count': pg_result[5]
                            }
                            
                            pg_all_correct = True
                            logger.info("\nPostgreSQL数据验证:")
                            for field, expected_value in pg_expected.items():
                                actual_value = pg_actual[field]
                                status = "✓" if actual_value == expected_value else "✗"
                                if actual_value != expected_value:
                                    pg_all_correct = False
                                logger.info(f"  {field}: 期望={expected_value}, 实际={actual_value} {status}")
                            
                            logger.info(f"\nPostgreSQL数据验证: {'✓ 正确' if pg_all_correct else '✗ 错误'}")
                else:
                    logger.error("PostgreSQL中未找到job_file_id=71973的记录")
            else:
                logger.error("数据提取错误，需要进一步调试")
                
        else:
            logger.warning("未找到job_file_id=71973的记录")
            # 显示前几条记录的job_file_id以供参考
            logger.info("前10条记录的job_file_id:")
            for i, record in enumerate(job_data[:10]):
                logger.info(f"  {i+1}: job_file_id={record.get('job_file_id')}, job_file_no={record.get('job_file_no')}")
        
        logger.info("测试完成")
        
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(test_scheduler_april_2025())
